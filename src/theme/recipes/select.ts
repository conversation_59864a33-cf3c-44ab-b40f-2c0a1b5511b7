import { defineSlotRecipe } from '@chakra-ui/react'
import { selectAnatomy } from '@chakra-ui/react/anatomy'
import { fieldStyle } from './input'

const lg = {
  fontSize: '1rem',
  lineHeight: '1.088125',
}

const md = {
  fontSize: '0.875rem',
  lineHeight: '1',
}

const sm = {
  fontSize: '0.8125rem',
  lineHeight: '1.08769230769',
}

const table = {
  borderRadius: 'none !important',
  borderColor: '#F2F2F4',
  height: '100% !important',
  color: 'typography.300',
  fontSize: '0.875rem',
  lineHeight: 1.5714285714,
  fontWeight: 600,
  _focus: { boxShadow: 'none', borderColor: 'primary.200' },
}

export const selectSlotRecipe = defineSlotRecipe({
  slots: selectAnatomy.keys(),
  base: {
    trigger: {
      ...fieldStyle,
      cursor: 'pointer',
    },
    content: {
      background: 'white',
      borderRadius: '8px',
      borderWidth: '1px',
      borderColor: 'border.box',
      boxShadow: '0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)',
      zIndex: 'dropdown',
      maxH: '96',
      overflowY: 'auto',
    },
    item: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      px: '1rem',
      py: '0.75rem',
      cursor: 'pointer',
      _hover: {
        backgroundColor: '#FAFBFC',
      },
      _highlighted: {
        backgroundColor: '#FAFBFC',
      },
      _selected: {
        backgroundColor: '#FAFBFC',
      },
    },
    valueText: {
      textAlign: 'start',
    },
    indicatorGroup: {
      display: 'flex',
      alignItems: 'center',
      gap: '2',
    },    
    indicator: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'typography.300',
    },
    itemIndicator: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'primary.200',
    },
  },
  variants: {
    size: {
      sm: { 
        control: sm,
        trigger: sm,
        item: sm,
      },
      md: { 
        control: md,
        trigger: md,
        item: md,
      },
      lg: { 
        control: lg,
        trigger: lg,
        item: lg,
      },
    },
    variant: { 
      table: { 
        control: table,
        trigger: table,
        item: { bg: 'background.300' },
      }
    },
  },
  defaultVariants: {
    size: 'md',
  },
})
