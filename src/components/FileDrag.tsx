import { toaster } from '@/components/ui/toaster'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Circle, FileUpload, Flex, Image } from '@chakra-ui/react'
import { UploadCloudIcon } from './EbanaIcons'

export default function FileDrag(props) {
  const {
    onChange,
    onSizeError = null,
    name = null,
    fileTypes = undefined,
    maxFileSize = 10,
    py = '1em',
    fileOrFiles = null,
    isButton = false,
    disabled = false,
    height = null,
  } = props

  const { t } = useEbanaLocale()

  const customContainer = (
    <Flex
      direction='column'
      alignItems='center'
      gap='0.25em'
      borderColor='primary.200'
      borderWidth='1px'
      borderStyle='dashed'
      background='background.300'
      py={py}
      px='1.15625em'
      borderRadius='12px'
      cursor='pointer'
      width='100%'
      height={height}>
      <Circle borderWidth='6px' borderColor='#E5F9F7' p='0.45rem' bg='white'>
        <UploadCloudIcon />
      </Circle>
      <Flex
        textStyle='body4'
        color='#323B41'
        alignItems='center'
        gap='0.2em'
        mt='0.5em'
        flexWrap='wrap'
        justifyContent='center'>
        {t('drag_file_title')}
        <Box textStyle='body4' fontWeight={500} color='primary.200'>
          {t('choose_file')}
        </Box>
      </Flex>
      <Box dir='ltr' textStyle='body5' color='#323B41'>
        {fileTypes && fileTypes.join(', ')} ( {maxFileSize} MB {t('file_maximum_size')} )
      </Box>
    </Flex>
  )

  const buttonContainer = (
    <Flex
      cursor='pointer'
      borderWidth='1px'
      borderColor='transparent'
      paddingX='1em'
      textStyle='body3'
      fontWeight={600}
      color='primary.200'
      alignContent='center'
      gap='0.5em'>
      <Box height='100%' pt='0.5em' alignContent='center' justifyContent='center'>
        <Image height='0.8em' src='/assets/primary-add.svg' alt='' />
      </Box>
      <Box>{t('add')}</Box>
    </Flex>
  )

  return (
    <Box position='relative' style={disabled ? { filter: 'blur(2px)' } : null} width='100%'>
      {disabled ? (
        <Box position='absolute' height='100%' width='100%' zIndex={99} backgroundColor='transparent'></Box>
      ) : (
        <></>
      )}

      <FileUpload.Root
        style={{
          margin: '-70px 0',
          padding: 0,
        }}
        onFileChange={(e) => {
          if (e.acceptedFiles.length > 0) {
            onChange(e.acceptedFiles[0])
          }
        }}
        name={name}
        accept={fileTypes}
        maxFileSize={maxFileSize * 1024 * 1024}
        alignItems='stretch'
        onFileReject={(details) => {
          if (details.files) {
            if (details.files[0].errors[0] == 'FILE_TOO_LARGE' && onSizeError) {
              onSizeError()
            } else {
              toaster.create({
                title: details.files[0].errors[0],
                type: 'error',
              })
            }
          }
        }}>
        <FileUpload.HiddenInput />
        <FileUpload.Dropzone
          style={{
            border: 'none',
            background: 'none',
          }}>
          {isButton ? buttonContainer : customContainer}
        </FileUpload.Dropzone>
      </FileUpload.Root>
    </Box>
  )
}
