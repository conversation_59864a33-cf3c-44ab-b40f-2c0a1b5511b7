import { Flex, Skeleton, SkeletonText, Stack } from '@chakra-ui/react'

export default function FullLoading({ full = false }) {
  return (
    <Stack w='100%' h='100%' p={{ base: '1rem', md: '1rem' }} gap='1rem'>
      <Flex
        justifyContent='space-between'
        flexWrap={{ base: 'wrap', md: 'nowrap' }}
        gap={{ base: '1rem', md: '1.875rem' }}
        mt={{ base: '1rem', md: '0px' }}>
        <Skeleton
          h='100vh'
          w='15rem'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='secondary.200'
        />
        {/* main content */}
        <Stack w='100%' gap='2rem'>
          <SkeletonText noOfLines={1} />
          <SkeletonText noOfLines={1} />
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
        </Stack>
        {/* side content */}
        <Stack
          mb='1.4375em'
          flex='1 1 0'
          flexWrap='wrap'
          gap={{ base: '1rem', md: '1.875rem' }}
          maxWidth={{ base: '100%', md: '21.875rem' }}
          width={{ base: '100%', md: '21.875rem' }}
          minW={{ base: '100%', md: '21.875rem' }}>
          <SkeletonText noOfLines={1} />
          <SkeletonText noOfLines={1} />
          <Skeleton
            h='30rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
          <Skeleton
            h='5rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 4px 16px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
          <Skeleton
            h='30rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
        </Stack>
      </Flex>
    </Stack>
  )
}
