import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { formSupport } from '@/util/formSupport'
import { HStack, IconButton, InputGroup, InputProps, NumberInput } from '@chakra-ui/react'
import { Controller, useFormContext } from 'react-hook-form'
import { LuMinus, LuPlus } from 'react-icons/lu'
import EbanaFormControl, { FieldProps } from '.'

type Props = FieldProps &
  NumberInput.InputProps &
  NumberInput.RootProps &
  InputProps & {
    withPlusMinus?: boolean
    withAddon?: boolean
    addonValue?: any
    addonPosition?: 'start' | 'end'
  }

export function EbanaNumberField(props: Props) {
  const { t } = useEbanaLocale()

  const {
    field,
    indices = [],
    fcProps = {},
    flProps = {},
    withPlusMinus,
    withAddon,
    addonValue,
    addonPosition = 'start',
    ...rest
  } = props

  const {
    register: rhfRegister,
    formState: { errors },
    setError,
    clearErrors,
    control,
  } = useFormContext()

  // Allowed states in RHF: number | null
  // Allowed states in NumberInput: number | string
  const makeOnChange = (onChange) => (event) => {
    const {
      target: { value },
    } = event
    if (value === '') {
      onChange(null)
    } else if (value.endsWith('.')) {
      onChange(value)
    } else {
      onChange(Number(value))
    }
  }

  const { computePath } = formSupport(errors, rhfRegister)

  return (
    <EbanaFormControl field={field} indices={indices} flProps={flProps}>
      <Controller
        name={computePath(field, indices)}
        control={control}
        rules={{
          required: {
            value: field.required,
            message: field.requiredMessage,
          },
        }}
        render={({ field: { ref, value, onChange, ...restField } }) =>
          withPlusMinus ? (
            <NumberInput.Root
              allowMouseWheel={false}
              value={value === null ? '' : value}
              onValueChange={({ value }) => makeOnChange(onChange)({ target: { value } })}
              onBlur={() => {
                if (isNaN(Number(value))) {
                  setError(computePath(field, indices), {
                    message: t('numeric_input_contains_text'),
                  })
                } else {
                  clearErrors(computePath(field, indices))
                }
              }}
              position='relative'
              {...rest}>
              <HStack gap='0'>
                <NumberInput.DecrementTrigger
                  borderColor='transparent'
                  borderStartRadius={14}
                  borderEndRadius={0}
                  asChild>
                  <IconButton variant='outline' size='sm'>
                    <LuMinus />
                  </IconButton>
                </NumberInput.DecrementTrigger>
                <NumberInput.ValueText
                  textAlign='center'
                  fontSize='lg'
                  w='100%'
                  ref={ref}
                  name={restField.name}
                  {...rest}
                />
                <NumberInput.IncrementTrigger
                  borderColor='transparent'
                  borderStartRadius={0}
                  borderEndRadius={14}
                  asChild>
                  <IconButton variant='outline' size='sm'>
                    <LuPlus />
                  </IconButton>
                </NumberInput.IncrementTrigger>
              </HStack>
            </NumberInput.Root>
          ) : withAddon ? (
            <InputGroup
              w='100%'
              startAddonProps={{
                borderStartRadius: 14,
                px: 6,
                background: 'transparent',
              }}
              endAddonProps={{
                borderEndRadius: 14,
                px: 6,
                background: 'transparent',
              }}
              {...rest}>
              <NumberInput.Root
                value={value === null ? '' : value}
                onValueChange={({ value }) => makeOnChange(onChange)({ target: { value } })}
                onBlur={() => {
                  if (isNaN(Number(value))) {
                    setError(computePath(field, indices), {
                      message: t('numeric_input_contains_text'),
                    })
                  } else {
                    clearErrors(computePath(field, indices))
                  }
                }}
                {...rest}
                dir='ltr'>
                <NumberInput.Input dir='ltr' ref={ref} name={restField.name} />
              </NumberInput.Root>
            </InputGroup>
          ) : (
            <NumberInput.Root
              value={value === null ? '' : value}
              onValueChange={({ value }) => makeOnChange(onChange)({ target: { value } })}
              onBlur={() => {
                if (isNaN(Number(value))) {
                  setError(computePath(field, indices), {
                    message: t('numeric_input_contains_text'),
                  })
                } else {
                  clearErrors(computePath(field, indices))
                }
              }}
              {...rest}>
              <NumberInput.Input ref={ref} name={restField.name} {...rest} />
            </NumberInput.Root>
          )
        }
      />
    </EbanaFormControl>
  )
}
