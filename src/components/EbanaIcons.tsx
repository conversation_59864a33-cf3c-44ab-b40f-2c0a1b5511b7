import { createIcon } from '@chakra-ui/react'

export const EbanaIcon = createIcon({
  displayName: 'EbanaIcon',
  viewBox: '0 0 23 24',
  path: (
    <>
      <g clipPath='url(#clip0_9301_114466)'>
        <path
          d='M11.0436 11.6086L9.36492 13.3002C9.352 13.3131 9.352 13.3131 9.33909 13.3131H5.31033C5.24577 13.3131 5.20703 13.2614 5.20703 13.2098V10.9759C5.20703 10.9113 5.25868 10.8726 5.31033 10.8726H9.59735L6.25296 7.52821C6.21422 7.48947 6.21422 7.41199 6.25296 7.37325L7.80248 5.82373C7.84122 5.78499 7.9187 5.78499 7.95743 5.82373L11.0436 8.90986C11.0565 8.92278 11.0565 8.92278 11.0565 8.93569V11.5828C11.0565 11.5957 11.0565 11.6086 11.0436 11.6086Z'
          fill='#6490E8'
        />
        <path
          d='M11.9465 11.6086L13.6381 13.3001C13.651 13.3131 13.651 13.3131 13.6639 13.3131H17.6927C17.7572 13.3131 17.796 13.2614 17.796 13.2098V10.9759C17.796 10.9113 17.7443 10.8726 17.6927 10.8726H13.3927L16.7242 7.54108C16.7629 7.50235 16.7629 7.42487 16.7242 7.38613L15.1747 5.83661C15.1359 5.79787 15.0585 5.79787 15.0197 5.83661L11.9465 8.90983C11.9336 8.92274 11.9336 8.92274 11.9336 8.93565V11.5828C11.9336 11.5957 11.9336 11.6086 11.9465 11.6086Z'
          fill='#00BFB2'
        />
        <path
          d='M11.4948 16.0765L13.6642 18.2458C13.69 18.2717 13.7158 18.2717 13.7416 18.2717H17.5121C17.538 18.2717 17.5638 18.2329 17.538 18.2071L11.4948 12.1769L5.46461 18.22C5.43878 18.2458 5.4517 18.2846 5.49043 18.2846H9.26094C9.28676 18.2846 9.31259 18.2717 9.33841 18.2588L11.4948 16.0765Z'
          fill='#004677'
        />
      </g>
    </>
  ),
  defaultProps: {
    width: '23px',
    height: '24px',
  },
})

export const EbanaDarkIcon = createIcon({
  displayName: 'EbanaDarkIcon',
  viewBox: '0 0 53 53',
  path: (
    <>
      <g clipPath='url(#clip0_26910_192046)'>
        <path
          d='M26.5 53C11.873 53 0 41.1544 0 26.5C0 11.8456 11.873 0 26.5 0C41.127 0 53 11.8762 53 26.5C53 41.1238 41.127 53 26.5 53Z'
          fill='typography.100'
        />
        <path
          d='M25.3379 25.494L21.3293 29.5239C21.2987 29.5544 21.2987 29.5544 21.2681 29.5544H11.6901C11.5371 29.5544 11.4453 29.4323 11.4453 29.3102V23.998C11.4453 23.8453 11.5677 23.7537 11.6901 23.7537H21.8801L13.9239 15.7854C13.8321 15.6938 13.8321 15.5107 13.9239 15.4191L17.6266 11.7249C17.7184 11.6334 17.902 11.6334 17.9938 11.7249L25.3379 19.0827C25.3685 19.1132 25.3685 19.1132 25.3685 19.1437V25.4634C25.3685 25.4634 25.3685 25.494 25.3379 25.494Z'
          fill='#6490E8'
        />
        <path
          d='M27.4798 25.4932L31.4885 29.5232C31.5191 29.5537 31.5191 29.5537 31.5497 29.5537H41.1276C41.2806 29.5537 41.3724 29.4316 41.3724 29.3095V24.0278C41.3724 23.8751 41.25 23.7836 41.1276 23.7836H30.9377L38.8938 15.8152C38.9856 15.7236 38.9856 15.5405 38.8938 15.4489L35.1911 11.6937C35.0993 11.6021 34.9157 11.6021 34.8239 11.6937L27.4798 19.0514C27.4492 19.0819 27.4492 19.0819 27.4492 19.1125V25.4322C27.4492 25.4627 27.4798 25.4932 27.4798 25.4932Z'
          fill='#00BFB2'
        />
        <path
          d='M26.4082 36.1475L31.5491 41.307C31.6103 41.3681 31.6715 41.3681 31.7327 41.3681H40.7292C40.821 41.3681 40.8516 41.2765 40.7904 41.2154L26.4082 26.8358L12.026 41.246C11.9648 41.307 11.9954 41.3986 12.0872 41.3986H21.0837C21.1449 41.3986 21.2061 41.3681 21.2673 41.3375L26.4082 36.1475Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_26910_192046'>
          <rect width='53' height='53' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '53px',
    height: '53px',
  },
})

export const TooltipFilledIcon = createIcon({
  displayName: 'TooltipFilledIcon',
  viewBox: '0 0 16 16',
  path: (
    <path
      fill-rule='evenodd'
      clip-rule='evenodd'
      d='M14.4001 8.0001C14.4001 11.5347 11.5347 14.4001 8.0001 14.4001C4.46548 14.4001 1.6001 11.5347 1.6001 8.0001C1.6001 4.46548 4.46548 1.6001 8.0001 1.6001C11.5347 1.6001 14.4001 4.46548 14.4001 8.0001ZM8.8001 4.8001C8.8001 5.24193 8.44192 5.6001 8.0001 5.6001C7.55827 5.6001 7.2001 5.24193 7.2001 4.8001C7.2001 4.35827 7.55827 4.0001 8.0001 4.0001C8.44192 4.0001 8.8001 4.35827 8.8001 4.8001ZM7.2001 7.2001C6.75827 7.2001 6.4001 7.55827 6.4001 8.0001C6.4001 8.44193 6.75827 8.8001 7.2001 8.8001V11.2001C7.2001 11.6419 7.55827 12.0001 8.0001 12.0001H8.8001C9.24192 12.0001 9.6001 11.6419 9.6001 11.2001C9.6001 10.7583 9.24192 10.4001 8.8001 10.4001V8.0001C8.8001 7.55827 8.44192 7.2001 8.0001 7.2001H7.2001Z'
      fill='typography.100'
    />
  ),
  defaultProps: {
    width: '22px',
    height: '22px',
  },
})

export const TooltipSmallVarIcon = createIcon({
  displayName: 'TooltipSmallVarIcon',
  viewBox: '0 0 16 16',
  path: (
    <path
      d='M8.66667 10.6667H8V8H7.33333M8 5.33333H8.00667M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8Z'
      stroke='#4E5D78'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
      fill='transparent'
    />
  ),
  defaultProps: {
    width: '16px',
    height: '16px',
  },
})

export const HelpIcon = createIcon({
  displayName: 'HelpIcon',
  viewBox: '0 0 16 16',
  path: (
    <svg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#clip0_31529_152665)'>
        <path
          d='M4.545 4.5C4.66255 4.16583 4.89458 3.88405 5.19998 3.70457C5.50538 3.52508 5.86445 3.45947 6.21359 3.51936C6.56273 3.57924 6.87941 3.76076 7.10754 4.03176C7.33567 4.30277 7.46053 4.64576 7.46 5C7.46 6 5.96 6.5 5.96 6.5M6 8.5H6.005M11 6C11 8.76142 8.76142 11 6 11C3.23858 11 1 8.76142 1 6C1 3.23858 3.23858 1 6 1C8.76142 1 11 3.23858 11 6Z'
          stroke='#B0B7C3'
          strokeWidth='1.33333'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_31529_152665'>
          <rect width='12' height='12' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  defaultProps: {
    width: '16px',
    height: '16px',
  },
})

export const DeleteIcon = createIcon({
  displayName: 'CustomIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d='M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path
        d='M9.16992 14.8299L14.8299 9.16992'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path
        d='M14.8299 14.8299L9.16992 9.16992'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
    stroke: '#FF5630',
  },
})

export const FacebookIcon = createIcon({
  displayName: 'FacebookIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <g clipPath='url(#clip0_529_77093)'>
        <path
          d='M20 10C20 4.47715 15.5229 0 10 0C4.47715 0 0 4.47715 0 10C0 14.9912 3.65684 19.1283 8.4375 19.8785V12.8906H5.89844V10H8.4375V7.79688C8.4375 5.29063 9.93047 3.90625 12.2146 3.90625C13.3084 3.90625 14.4531 4.10156 14.4531 4.10156V6.5625H13.1922C11.95 6.5625 11.5625 7.3334 11.5625 8.125V10H14.3359L13.8926 12.8906H11.5625V19.8785C16.3432 19.1283 20 14.9912 20 10Z'
          fill='typography.100'
        />
      </g>
      <defs>
        <clipPath id='clip0_529_77093'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
  },
})

export const XIcon = createIcon({
  displayName: 'CustomIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <g clipPath='url(#clip0_6731_65486)'>
        <path
          d='M24 11.2599C24 11.7611 24 12.2576 24 12.7588C23.9859 12.8196 23.9672 12.8805 23.9625 12.9414C23.8031 14.9976 23.1703 16.8946 22.0172 18.6042C20.1047 21.4379 17.4609 23.185 14.0906 23.8173C13.65 23.9016 13.1953 23.9438 12.75 24.0047C12.2484 24.0047 11.7516 24.0047 11.25 24.0047C11.1891 23.9906 11.1328 23.9719 11.0719 23.9672C8.68125 23.7751 6.52969 22.9602 4.64531 21.4801C2.21719 19.5691 0.721875 17.0913 0.178125 14.0515C0.103125 13.6253 0.05625 13.1897 0 12.7588C0 12.2576 0 11.7611 0 11.2599C0.028125 11.0445 0.05625 10.829 0.084375 10.6136C0.515625 7.20372 2.10469 4.44494 4.85156 2.38873C8.71875 -0.5012 13.8422 -0.796282 18.0141 1.644C21.2016 3.50817 23.1328 6.30442 23.8172 9.94376C23.8969 10.3794 23.9391 10.8196 24 11.2599ZM12.7734 10.1358C12.7125 10.0468 12.6562 9.97187 12.6047 9.89224C11.5219 8.31847 10.4391 6.7447 9.35625 5.16625C9.25313 5.01636 9.15 4.95079 8.95781 4.95079C7.7625 4.96016 6.56719 4.95547 5.37187 4.95547C5.29688 4.95547 5.22188 4.96484 5.1 4.96953C6.90469 7.59248 8.68594 10.1873 10.4813 12.7915C8.69531 14.8665 6.91406 16.9321 5.1 19.0445C5.475 19.0445 5.78906 19.0351 6.10781 19.0492C6.2625 19.0538 6.36562 18.9976 6.46406 18.8805C7.79062 17.3349 9.12187 15.7892 10.4484 14.2482C10.6359 14.0328 10.8234 13.8126 11.025 13.5831C11.0953 13.6815 11.1469 13.7564 11.1984 13.836C12.3516 15.5129 13.5094 17.1897 14.6578 18.8712C14.7469 19.0023 14.8406 19.0538 15 19.0538C16.2188 19.0492 17.4375 19.0492 18.6562 19.0492C18.7219 19.0492 18.7875 19.0398 18.8859 19.0304C17.0156 16.3091 15.1641 13.6206 13.3172 10.9274C15.0281 8.94142 16.7203 6.97421 18.4547 4.95547C18.0797 4.95547 17.7656 4.96484 17.4469 4.95079C17.2875 4.94611 17.1891 5.00231 17.0906 5.11941C16.2656 6.08896 15.4312 7.04915 14.6016 8.01402C13.9969 8.7166 13.3922 9.41917 12.7734 10.1358Z'
          fill='typography.100'
        />
        <path
          d='M17.2123 18.1546C17.1232 18.1639 17.067 18.1733 17.0061 18.1733C16.5232 18.1733 16.0357 18.1827 15.5529 18.1686C15.4498 18.1639 15.3139 18.0937 15.2529 18.0094C12.4592 14.0328 9.6748 10.0468 6.89043 6.06558C6.85762 6.01875 6.8248 5.96254 6.76855 5.87355C7.29824 5.87355 7.79512 5.8876 8.2873 5.86886C8.53574 5.8595 8.67637 5.9438 8.81699 6.14521C11.5545 10.075 14.3061 14 17.0482 17.9204C17.1045 17.9906 17.1514 18.0609 17.2123 18.1546Z'
          fill='typography.100'
        />
      </g>
      <defs>
        <clipPath id='clip0_6731_65486'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const InstagramIcon = createIcon({
  displayName: 'InstagramIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d='M12 2.16094C15.2063 2.16094 15.5859 2.175 16.8469 2.23125C18.0188 2.28281 18.6516 2.47969 19.0734 2.64375C19.6313 2.85938 20.0344 3.12188 20.4516 3.53906C20.8734 3.96094 21.1313 4.35938 21.3469 4.91719C21.5109 5.33906 21.7078 5.97656 21.7594 7.14375C21.8156 8.40937 21.8297 8.78906 21.8297 11.9906C21.8297 15.1969 21.8156 15.5766 21.7594 16.8375C21.7078 18.0094 21.5109 18.6422 21.3469 19.0641C21.1313 19.6219 20.8687 20.025 20.4516 20.4422C20.0297 20.8641 19.6313 21.1219 19.0734 21.3375C18.6516 21.5016 18.0141 21.6984 16.8469 21.75C15.5813 21.8062 15.2016 21.8203 12 21.8203C8.79375 21.8203 8.41406 21.8062 7.15313 21.75C5.98125 21.6984 5.34844 21.5016 4.92656 21.3375C4.36875 21.1219 3.96563 20.8594 3.54844 20.4422C3.12656 20.0203 2.86875 19.6219 2.65313 19.0641C2.48906 18.6422 2.29219 18.0047 2.24063 16.8375C2.18438 15.5719 2.17031 15.1922 2.17031 11.9906C2.17031 8.78438 2.18438 8.40469 2.24063 7.14375C2.29219 5.97187 2.48906 5.33906 2.65313 4.91719C2.86875 4.35938 3.13125 3.95625 3.54844 3.53906C3.97031 3.11719 4.36875 2.85938 4.92656 2.64375C5.34844 2.47969 5.98594 2.28281 7.15313 2.23125C8.41406 2.175 8.79375 2.16094 12 2.16094ZM12 0C8.74219 0 8.33438 0.0140625 7.05469 0.0703125C5.77969 0.126563 4.90313 0.332812 4.14375 0.628125C3.35156 0.9375 2.68125 1.34531 2.01563 2.01562C1.34531 2.68125 0.9375 3.35156 0.628125 4.13906C0.332812 4.90313 0.126563 5.775 0.0703125 7.05C0.0140625 8.33437 0 8.74219 0 12C0 15.2578 0.0140625 15.6656 0.0703125 16.9453C0.126563 18.2203 0.332812 19.0969 0.628125 19.8563C0.9375 20.6484 1.34531 21.3188 2.01563 21.9844C2.68125 22.65 3.35156 23.0625 4.13906 23.3672C4.90313 23.6625 5.775 23.8687 7.05 23.925C8.32969 23.9812 8.7375 23.9953 11.9953 23.9953C15.2531 23.9953 15.6609 23.9812 16.9406 23.925C18.2156 23.8687 19.0922 23.6625 19.8516 23.3672C20.6391 23.0625 21.3094 22.65 21.975 21.9844C22.6406 21.3188 23.0531 20.6484 23.3578 19.8609C23.6531 19.0969 23.8594 18.225 23.9156 16.95C23.9719 15.6703 23.9859 15.2625 23.9859 12.0047C23.9859 8.74688 23.9719 8.33906 23.9156 7.05938C23.8594 5.78438 23.6531 4.90781 23.3578 4.14844C23.0625 3.35156 22.6547 2.68125 21.9844 2.01562C21.3188 1.35 20.6484 0.9375 19.8609 0.632812C19.0969 0.3375 18.225 0.13125 16.95 0.075C15.6656 0.0140625 15.2578 0 12 0Z'
        fill='typography.100'
      />
      <path
        d='M12 5.83594C8.59688 5.83594 5.83594 8.59688 5.83594 12C5.83594 15.4031 8.59688 18.1641 12 18.1641C15.4031 18.1641 18.1641 15.4031 18.1641 12C18.1641 8.59688 15.4031 5.83594 12 5.83594ZM12 15.9984C9.79219 15.9984 8.00156 14.2078 8.00156 12C8.00156 9.79219 9.79219 8.00156 12 8.00156C14.2078 8.00156 15.9984 9.79219 15.9984 12C15.9984 14.2078 14.2078 15.9984 12 15.9984Z'
        fill='typography.100'
      />
      <path
        d='M19.8469 5.59214C19.8469 6.38902 19.2 7.0312 18.4078 7.0312C17.6109 7.0312 16.9688 6.38433 16.9688 5.59214C16.9688 4.79526 17.6156 4.15308 18.4078 4.15308C19.2 4.15308 19.8469 4.79995 19.8469 5.59214Z'
        fill='typography.100'
      />
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const LinkedInIcon = createIcon({
  displayName: 'LinkedInIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <g clipPath='url(#clip0_529_77095)'>
        <path
          d='M18.5195 0H1.47656C0.660156 0 0 0.644531 0 1.44141V18.5547C0 19.3516 0.660156 20 1.47656 20H18.5195C19.3359 20 20 19.3516 20 18.5586V1.44141C20 0.644531 19.3359 0 18.5195 0ZM5.93359 17.043H2.96484V7.49609H5.93359V17.043ZM4.44922 6.19531C3.49609 6.19531 2.72656 5.42578 2.72656 4.47656C2.72656 3.52734 3.49609 2.75781 4.44922 2.75781C5.39844 2.75781 6.16797 3.52734 6.16797 4.47656C6.16797 5.42187 5.39844 6.19531 4.44922 6.19531ZM17.043 17.043H14.0781V12.4023C14.0781 11.2969 14.0586 9.87109 12.5352 9.87109C10.9922 9.87109 10.7578 11.0781 10.7578 12.3242V17.043H7.79688V7.49609H10.6406V8.80078H10.6797C11.0742 8.05078 12.043 7.25781 13.4844 7.25781C16.4883 7.25781 17.043 9.23438 17.043 11.8047V17.043V17.043Z'
          fill='typography.100'
        />
      </g>
      <defs>
        <clipPath id='clip0_529_77095'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
  },
})

export const BellIcon = createIcon({
  displayName: 'BellIcon',
  viewBox: '0 0 149 50',
  path: (
    <>
      <rect width='24' height='24' fill='url(#pattern0_33542_7823)' />
      <defs>
        <pattern id='pattern0_33542_7823' patternContentUnits='objectBoundingBox' width='1' height='1'>
          <use href='#image0_33542_7823' transform='scale(0.0138889)' />
        </pattern>
        <image
          id='image0_33542_7823'
          width='72'
          height='72'
          preserveAspectRatio='none'
          href='data:image/png;base64,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'
        />
      </defs>
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
    viewBox: '0 0 24 24',
    fill: 'none',
  },
})

export const YouTubeIcon = createIcon({
  displayName: 'YouTubeIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M19.8008 6C19.8008 6 19.6055 4.62109 19.0039 4.01563C18.2422 3.21875 17.3906 3.21484 17 3.16797C14.2031 2.96484 10.0039 2.96484 10.0039 2.96484H9.99609C9.99609 2.96484 5.79687 2.96484 3 3.16797C2.60938 3.21484 1.75781 3.21875 0.996094 4.01563C0.394531 4.62109 0.203125 6 0.203125 6C0.203125 6 0 7.62109 0 9.23828V10.7539C0 12.3711 0.199219 13.9922 0.199219 13.9922C0.199219 13.9922 0.394531 15.3711 0.992187 15.9766C1.75391 16.7734 2.75391 16.7461 3.19922 16.832C4.80078 16.9844 10 17.0313 10 17.0313C10 17.0313 14.2031 17.0234 17 16.8242C17.3906 16.7773 18.2422 16.7734 19.0039 15.9766C19.6055 15.3711 19.8008 13.9922 19.8008 13.9922C19.8008 13.9922 20 12.375 20 10.7539V9.23828C20 7.62109 19.8008 6 19.8008 6ZM7.93359 12.5938V6.97266L13.3359 9.79297L7.93359 12.5938Z'
      fill='typography.100'
    />
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const SnapchatIcon = createIcon({
  displayName: 'SnapchatIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      d='M23.9132 17.4572C23.7478 17.0037 23.4291 16.7585 23.0675 16.5624C23.0001 16.5256 22.9388 16.4889 22.8836 16.4643C22.7733 16.4092 22.663 16.354 22.5527 16.2989C21.425 15.6983 20.5425 14.9506 19.9358 14.0558C19.7642 13.8045 19.611 13.5349 19.4884 13.2591C19.4332 13.112 19.4394 13.0262 19.4761 12.9465C19.5129 12.8853 19.5619 12.8362 19.6232 12.7933C19.8193 12.6646 20.0155 12.5359 20.1503 12.4501C20.3893 12.2908 20.5854 12.1682 20.708 12.0824C21.1676 11.7576 21.4924 11.4144 21.6947 11.0283C21.9827 10.489 22.0195 9.85773 21.7989 9.28777C21.4924 8.4788 20.7325 7.98238 19.8071 7.98238C19.611 7.98238 19.421 8.00077 19.2249 8.04367C19.1758 8.05592 19.1207 8.06818 19.0716 8.08044C19.0778 7.52887 19.0655 6.94665 19.0165 6.37056C18.8449 4.35426 18.134 3.30014 17.3985 2.46052C16.9266 1.93347 16.3751 1.48608 15.7561 1.13675C14.6407 0.499376 13.3721 0.174561 11.9931 0.174561C10.6142 0.174561 9.35171 0.499376 8.2363 1.13675C7.61732 1.48608 7.06574 1.93347 6.59384 2.46052C5.85841 3.30014 5.15363 4.36039 4.9759 6.37056C4.92687 6.94665 4.91461 7.52887 4.92074 8.08044C4.87171 8.06818 4.82268 8.05592 4.76752 8.04367C4.57754 8.00077 4.38142 7.98238 4.19144 7.98238C3.26602 7.98238 2.50607 8.48493 2.19965 9.28777C1.97902 9.85773 2.01579 10.489 2.30383 11.0283C2.50607 11.4144 2.83089 11.7576 3.29053 12.0824C3.41311 12.1682 3.60309 12.2908 3.84824 12.4501C3.97694 12.5359 4.16692 12.6585 4.35691 12.7811C4.42432 12.824 4.47948 12.8791 4.52238 12.9465C4.55915 13.0262 4.56528 13.112 4.50399 13.2714C4.38142 13.541 4.23434 13.8045 4.06274 14.0497C3.46826 14.9199 2.61026 15.6615 1.51937 16.256C0.943285 16.5624 0.342683 16.7647 0.085282 17.4572C-0.104704 17.9781 0.0178675 18.5665 0.502026 19.069C0.679755 19.2529 0.888127 19.4122 1.11489 19.5348C1.58679 19.7922 2.08933 19.9944 2.61639 20.1354C2.7267 20.166 2.82476 20.2089 2.91669 20.2702C3.09442 20.4234 3.0699 20.6563 3.30279 20.9995C3.41923 21.1772 3.57245 21.3304 3.74405 21.453C4.24047 21.7962 4.79817 21.8146 5.38651 21.8391C5.9197 21.8575 6.5203 21.882 7.21283 22.1088C7.50087 22.2007 7.79505 22.3846 8.13825 22.5991C8.96561 23.1077 10.0933 23.8003 11.987 23.8003C13.8807 23.8003 15.0145 23.1016 15.848 22.5929C16.1912 22.3846 16.4854 22.2007 16.7612 22.1088C17.4476 21.882 18.0543 21.8575 18.5875 21.8391C19.1758 21.8146 19.7335 21.7962 20.23 21.453C20.4383 21.3059 20.6099 21.1221 20.7325 20.9014C20.9041 20.6134 20.898 20.4112 21.0573 20.2702C21.1431 20.2089 21.2412 20.166 21.3392 20.1415C21.8663 20.0005 22.3811 19.7983 22.8591 19.5348C23.0981 19.4061 23.3188 19.2345 23.5026 19.0322L23.5087 19.0261C23.9868 18.5358 24.1032 17.9597 23.9132 17.4572ZM22.234 18.3581C21.2105 18.9219 20.5241 18.8606 19.9971 19.2038C19.5436 19.4919 19.8132 20.117 19.4884 20.3437C19.0839 20.6195 17.895 20.3254 16.3628 20.834C15.0942 21.2508 14.2914 22.4581 12.0115 22.4581C9.73168 22.4581 8.94722 21.2569 7.66022 20.834C6.12807 20.3254 4.93912 20.6257 4.53464 20.3437C4.20982 20.117 4.47335 19.4919 4.02596 19.2038C3.49278 18.8606 2.8125 18.9219 1.78903 18.3581C1.13327 17.9965 1.50711 17.7759 1.72162 17.6717C5.43554 15.876 6.03001 13.0998 6.05453 12.8914C6.08517 12.6401 6.12194 12.444 5.84616 12.1927C5.58263 11.9476 4.40594 11.2183 4.07499 10.9915C3.53568 10.6115 3.29666 10.2377 3.47439 9.77193C3.59696 9.45324 3.89726 9.33067 4.20982 9.33067C4.30788 9.33067 4.40594 9.34293 4.50399 9.36131C5.09847 9.49001 5.67456 9.78419 6.0055 9.86999C6.0484 9.88225 6.08517 9.88837 6.12807 9.88837C6.3058 9.88837 6.36709 9.79644 6.35483 9.5942C6.31806 8.94457 6.22613 7.68208 6.33031 6.49926C6.47127 4.87519 6.9922 4.06621 7.61732 3.3553C7.91762 3.0121 9.32106 1.52898 12.0115 1.52898C14.702 1.52898 16.1054 3.00597 16.4057 3.34917C17.0308 4.06009 17.5518 4.86906 17.6927 6.49313C17.7969 7.67595 17.705 8.93844 17.6621 9.58807C17.6498 9.80257 17.7111 9.88224 17.8888 9.88224C17.9317 9.88224 17.9685 9.87612 18.0114 9.86386C18.3423 9.78419 18.9184 9.48389 19.5129 9.35519C19.611 9.33067 19.709 9.32454 19.8071 9.32454C20.1196 9.32454 20.4199 9.44711 20.5425 9.7658C20.7202 10.2316 20.4812 10.6054 19.9419 10.9854C19.6171 11.2121 18.4404 11.9415 18.1707 12.1866C17.895 12.4379 17.9317 12.634 17.9624 12.8853C17.9869 13.0936 18.5814 15.8699 22.2953 17.6656C22.5159 17.7697 22.8836 17.9965 22.234 18.3581Z'
      fill='typography.100'
    />
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const TikTokIcon = createIcon({
  displayName: 'TikTokIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      d='M17.0725 0H13.0278V16.3478C13.0278 18.2957 11.4722 19.8957 9.53626 19.8957C7.60034 19.8957 6.04469 18.2957 6.04469 16.3478C6.04469 14.4348 7.56577 12.8695 9.43257 12.8V8.69567C5.31872 8.7652 2 12.1391 2 16.3478C2 20.5913 5.38786 24 9.57085 24C13.7538 24 17.1416 20.5565 17.1416 16.3478V7.9652C18.6627 9.07827 20.5295 9.73913 22.5 9.77393V5.66957C19.4579 5.56522 17.0725 3.06087 17.0725 0Z'
      fill='typography.100'
    />
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const WhatsAppIcon = createIcon({
  displayName: 'WhatsAppIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path d='M24 0H0V24H24V0Z' fill='none' />
      <path
        d='M6.9403 20.63C8.4303 21.5 10.1603 22 12.0003 22C17.6303 22 22.3103 17.03 21.9803 11.41C21.6403 5.60997 16.3703 1.13996 10.3003 2.13996C6.12029 2.82996 2.77029 6.21996 2.12029 10.4C1.74029 12.82 2.24031 15.11 3.33031 17L2.4403 20.31C2.2403 21.06 2.93028 21.74 3.67028 21.53L6.9403 20.63Z'
        fill='typography.100'
      />
      <path
        d='M17 15.17C17 15.35 16.96 15.54 16.87 15.72C16.78 15.9 16.67 16.07 16.53 16.23C16.28 16.5 16.01 16.7 15.71 16.82C15.41 16.95 15.08 17.01 14.73 17.01C14.22 17.01 13.67 16.89 13.1 16.64C12.52 16.39 11.95 16.06 11.38 15.65C10.8 15.23 10.26 14.76 9.73999 14.25C9.21999 13.73 8.76003 13.18 8.34003 12.61C7.93003 12.04 7.59999 11.47 7.35999 10.9C7.11999 10.33 7 9.77998 7 9.25998C7 8.91998 7.05999 8.58998 7.17999 8.28998C7.29999 7.97998 7.49001 7.69997 7.76001 7.44997C8.08001 7.12997 8.42999 6.97998 8.79999 6.97998C8.93999 6.97998 9.08002 7.00998 9.21002 7.06998C9.34002 7.12998 9.45999 7.21998 9.54999 7.34998L10.7 8.99998C10.79 9.12998 10.86 9.23998 10.9 9.34998C10.95 9.45998 10.97 9.55998 10.97 9.65998C10.97 9.77998 10.93 9.89997 10.86 10.02C10.79 10.14 10.7 10.26 10.58 10.38L10.2 10.78C10.14 10.84 10.12 10.9 10.12 10.98C10.12 11.02 10.13 11.06 10.14 11.1C10.16 11.14 10.17 11.17 10.18 11.2C10.27 11.37 10.43 11.58 10.65 11.84C10.88 12.1 11.12 12.37 11.38 12.63C11.65 12.9 11.91 13.14 12.18 13.37C12.44 13.59 12.66 13.74 12.83 13.83C12.86 13.84 12.89 13.86 12.92 13.87C12.96 13.89 13 13.89 13.05 13.89C13.14 13.89 13.2 13.86 13.26 13.8L13.64 13.42C13.77 13.29 13.89 13.2 14 13.14C14.12 13.07 14.23 13.03 14.36 13.03C14.46 13.03 14.56 13.05 14.67 13.1C14.78 13.15 14.9 13.21 15.02 13.3L16.68 14.48C16.81 14.57 16.9 14.68 16.96 14.8C16.97 14.91 17 15.03 17 15.17Z'
        fill='white'
      />
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
  },
})

export const ArrowDownIcon = createIcon({
  displayName: 'ArrowDownIcon',
  viewBox: '0 0 19 18',
  path: (
    <path
      d='M15.4396 11.2875L10.5496 6.39748C9.97207 5.81998 9.02707 5.81998 8.44957 6.39748L3.55957 11.2875'
      stroke='white'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
      transform='rotate(180 9.5 9)'
    />
  ),
  defaultProps: {
    width: '16px',
    height: '15px',
    strokeWidth: '2.5',
    fill: 'transparent',
  },
})

export const YellowWarningIcon = createIcon({
  displayName: 'YellowWarningIcon',
  viewBox: '0 0 14 14',
  path: (
    <>
      <path
        d='M2.57555e-06 6.99836C0.00328511 3.12826 3.13482 -0.00327995 7.00164 2.57825e-06C10.8717 0.00328511 14 3.13482 14 7.00164C14 10.8717 10.8652 14.0033 6.99836 14C3.13154 13.9967 -0.00327996 10.8652 2.57555e-06 6.99836ZM12.6049 6.9918C12.5951 3.89637 10.0675 1.37867 6.9918 1.39508C3.89309 1.41149 1.37867 3.93576 1.39836 7.01477C1.41806 10.1069 3.92591 12.6082 7.00493 12.6049C10.0905 12.6016 12.6148 10.0741 12.6049 6.9918Z'
        fill='#FFAC00'
      />
      <path
        d='M6.30223 5.59348C6.30223 4.88773 6.29895 4.18199 6.30223 3.47624C6.30552 3.17425 6.49919 2.92478 6.77492 2.83287C7.05393 2.74096 7.36906 2.82302 7.53647 3.06265C7.61525 3.17425 7.68418 3.31868 7.68418 3.44998C7.69403 4.87789 7.69403 6.30579 7.68746 7.73369C7.68418 8.11118 7.36249 8.40333 6.99157 8.40004C6.61736 8.39676 6.30552 8.10461 6.30223 7.72712C6.29567 7.0181 6.30223 6.30579 6.30223 5.59348Z'
        fill='#FFAC00'
      />
      <path
        d='M7.69407 9.80158C7.69078 10.1922 7.38223 10.4975 6.9916 10.4942C6.60098 10.4909 6.29899 10.1824 6.30227 9.79173C6.30556 9.40111 6.61411 9.09912 7.00473 9.09912C7.39207 9.09912 7.69735 9.41096 7.69407 9.80158Z'
        fill='#FFAC00'
      />
    </>
  ),
  defaultProps: {
    width: '14px',
    height: '14px',
  },
})

export const TrashIcon = createIcon({
  displayName: 'TrashIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <path
        d='M17.5 5.09268C14.725 4.82268 11.9333 4.68359 9.15 4.68359C7.5 4.68359 5.85 4.76541 4.2 4.92905L2.5 5.09268'
        stroke='#FF5630'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.08398 4.26691L7.26732 3.1951C7.40065 2.41782 7.50065 1.83691 8.90898 1.83691H11.0923C12.5007 1.83691 12.609 2.45055 12.734 3.20328L12.9173 4.26691'
        stroke='#FF5630'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15.7077 7.67871L15.166 15.9178C15.0743 17.2023 14.9993 18.2005 12.6743 18.2005H7.32435C4.99935 18.2005 4.92435 17.2023 4.83268 15.9178L4.29102 7.67871'
        stroke='#FF5630'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8.60742 13.7002H11.3824'
        stroke='#FF5630'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.91602 10.4277H12.0827'
        stroke='#FF5630'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    stroke: '#FF5630',
    fill: 'transparent',
  },
})

export const SmallPenIcon = createIcon({
  displayName: 'SmallPenIcon',
  path: (
    <>
      <path
        d='M10.7051 17.1662H18.2051M3.20508 17.1662H4.60053C5.00818 17.1662 5.21201 17.1662 5.40382 17.1202C5.57388 17.0793 5.73645 17.012 5.88558 16.9206C6.05377 16.8175 6.1979 16.6734 6.48615 16.3852L16.9551 5.9162C17.6455 5.22585 17.6455 4.10656 16.9551 3.4162C16.2647 2.72585 15.1455 2.72585 14.4551 3.4162L3.98613 13.8852C3.69787 14.1734 3.55375 14.3175 3.45068 14.4857C3.3593 14.6349 3.29196 14.7974 3.25113 14.9675C3.20508 15.1593 3.20508 15.3631 3.20508 15.7708V17.1662Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    stroke: '#00BFB2',
    fill: 'transparent',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const StatusIcon = createIcon({
  displayName: 'StatusIcon',
  viewBox: '0 0 14 14',
  path: (
    <>
      <path
        opacity='0.4'
        d='M9.49159 13.4906C9.20874 13.2078 8.83159 13.0449 8.42874 13.0449H4.07445C3.55159 13.0449 3.07159 13.3106 2.79731 13.7564C2.52302 14.1935 2.49731 14.7163 2.72016 15.1792C3.77445 17.3392 5.67731 19.0363 7.94017 19.8335C8.09445 19.8849 8.26588 19.9192 8.42874 19.9192C8.72874 19.9192 9.02874 19.8249 9.28588 19.6449C9.68874 19.3621 9.92874 18.8992 9.92874 18.4106L9.93731 14.5535C9.92874 14.1506 9.77445 13.7735 9.49159 13.4906Z'
      />
      <path d='M19.9831 9.08488C19.0231 4.86773 15.3288 1.92773 11.0002 1.92773C6.67164 1.92773 2.97736 4.86773 2.01736 9.08488C1.9145 9.53059 2.01736 9.98488 2.30879 10.3449C2.60022 10.7049 3.02879 10.9106 3.49164 10.9106H18.5174C18.9802 10.9106 19.4088 10.7049 19.7002 10.3449C19.9831 9.98488 20.0859 9.52202 19.9831 9.08488Z' />
      <path
        opacity='0.4'
        d='M19.1951 13.7989C18.9208 13.3532 18.4408 13.0789 17.9094 13.0789L13.5723 13.0703C13.1694 13.0703 12.7923 13.2246 12.5094 13.5075C12.2266 13.7903 12.0723 14.1675 12.0723 14.5703L12.0808 18.4103C12.0808 18.8989 12.3208 19.3617 12.7237 19.6446C12.9808 19.8246 13.2808 19.9189 13.5808 19.9189C13.7437 19.9189 13.9066 19.8932 14.0608 19.8332C16.3066 19.0446 18.2094 17.356 19.2637 15.2217C19.4866 14.7675 19.4608 14.236 19.1951 13.7989Z'
      />
    </>
  ),
  defaultProps: {
    viewBox: null,
    width: '1.25em',
    height: '1.25em',
    fill: '#00BFB2',
  },
})

export const DragIcon = createIcon({
  displayName: 'DragIcon',
  viewBox: '0 0 14 14',
  path: (
    <>
      <circle cx='2' cy='2' r='2' />
      <circle cx='2' cy='10' r='2' />
      <circle cx='2' cy='18' r='2' />
      <circle cx='8' cy='2' r='2' />
      <circle cx='8' cy='10' r='2' />
      <circle cx='8' cy='18' r='2' />
    </>
  ),
  defaultProps: {
    width: '1em',
    height: '2em',
    fill: '#8A94A6',
  },
})

export const UserSquareIcon = createIcon({
  displayName: 'UserSquareIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <path
        d='M15.1171 18.0166C14.3838 18.2333 13.5171 18.3333 12.5005 18.3333H7.50045C6.48379 18.3333 5.61712 18.2333 4.88379 18.0166C5.06712 15.85 7.29212 14.1416 10.0005 14.1416C12.7088 14.1416 14.9338 15.85 15.1171 18.0166Z'
        stroke='#8A94A6'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path
        d='M12.5003 1.66663H7.50033C3.33366 1.66663 1.66699 3.33329 1.66699 7.49996V12.5C1.66699 15.65 2.61699 17.375 4.88366 18.0166C5.06699 15.85 7.29199 14.1416 10.0003 14.1416C12.7087 14.1416 14.9337 15.85 15.117 18.0166C17.3837 17.375 18.3337 15.65 18.3337 12.5V7.49996C18.3337 3.33329 16.667 1.66663 12.5003 1.66663ZM10.0003 11.8083C8.35033 11.8083 7.01699 10.4666 7.01699 8.81664C7.01699 7.16664 8.35033 5.83329 10.0003 5.83329C11.6503 5.83329 12.9837 7.16664 12.9837 8.81664C12.9837 10.4666 11.6503 11.8083 10.0003 11.8083Z'
        stroke='#8A94A6'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
  },
})

export const UpArrowIcon = createIcon({
  displayName: 'UpArrowIcon',
  viewBox: '0 0 18 18',
  path: (
    <path
      d='M3.05859 11.2891L7.94859 6.39914C8.52609 5.82164 9.47109 5.82164 10.0486 6.39914L14.9386 11.2891'
      strokeWidth='1.5'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
      fill='transparent'
    />
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    stroke: 'typography.100',
  },
})

export const DownArrowIcon = createIcon({
  displayName: 'DownArrowIcon',
  viewBox: '0 0 18 18',
  path: (
    <path
      d='M14.9386 6.71094L10.0486 11.6009C9.47109 12.1784 8.52609 12.1784 7.94859 11.6009L3.05859 6.71094'
      strokeWidth='1.5'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
      fill='transparent'
    />
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    stroke: 'typography.100',
  },
})

export const MeetingIcons = createIcon({
  displayName: 'MeetingIcons',
  viewBox: '0 0 117 24',
  path: (
    <>
      <path
        d='M12.53 20.4201H6.21C3.05 20.4201 2 18.3201 2 16.2101V7.79008C2 4.63008 3.05 3.58008 6.21 3.58008H12.53C15.69 3.58008 16.74 4.63008 16.74 7.79008V16.2101C16.74 19.3701 15.68 20.4201 12.53 20.4201Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M19.5202 17.0999L16.7402 15.1499V8.83989L19.5202 6.88989C20.8802 5.93989 22.0002 6.51989 22.0002 8.18989V15.8099C22.0002 17.4799 20.8802 18.0599 19.5202 17.0999Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M11.5 11C12.3284 11 13 10.3284 13 9.5C13 8.67157 12.3284 8 11.5 8C10.6716 8 10 8.67157 10 9.5C10 10.3284 10.6716 11 11.5 11Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M37.44 2H48.55C52.11 2 53 2.89 53 6.44V12.77C53 16.33 52.11 17.21 48.56 17.21H37.44C33.89 17.22 33 16.33 33 12.78V6.44C33 2.89 33.89 2 37.44 2Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path d='M43 17.22V22' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='transparent' />
      <path d='M33 13H53' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='transparent' />
      <path d='M38.5 22H47.5' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='transparent' />
      <path
        d='M80.4698 16.83L80.8598 19.99C80.9598 20.82 80.0698 21.4 79.3598 20.97L75.1698 18.48C74.7098 18.48 74.2599 18.45 73.8199 18.39C74.5599 17.52 74.9998 16.42 74.9998 15.23C74.9998 12.39 72.5398 10.09 69.4998 10.09C68.3398 10.09 67.2699 10.42 66.3799 11C66.3499 10.75 66.3398 10.5 66.3398 10.24C66.3398 5.68999 70.2898 2 75.1698 2C80.0498 2 83.9998 5.68999 83.9998 10.24C83.9998 12.94 82.6098 15.33 80.4698 16.83Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M75 15.23C75 16.42 74.56 17.5201 73.82 18.3901C72.83 19.5901 71.26 20.36 69.5 20.36L66.89 21.91C66.45 22.18 65.89 21.81 65.95 21.3L66.2 19.3301C64.86 18.4001 64 16.91 64 15.23C64 13.47 64.94 11.9201 66.38 11.0001C67.27 10.4201 68.34 10.0901 69.5 10.0901C72.54 10.0901 75 12.39 75 15.23Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M115 10V15C115 20 113 22 108 22H102C97 22 95 20 95 15V9C95 4 97 2 102 2H107'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path
        d='M115 10H111C108 10 107 9 107 6V2L115 10Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='transparent'
      />
      <path d='M100 13H106' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='transparent' />
      <path d='M100 17H104' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='transparent' />
    </>
  ),
  defaultProps: {
    width: '117px',
    height: '24px',
    stroke: '#B0B7C3',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const LinkIcon = createIcon({
  displayName: 'LinkIcon',
  viewBox: '0 0 40 40',
  path: (
    <>
      <path
        opacity='0.2'
        d='M26.983 3.33325H13.033C6.9663 3.33325 3.34961 6.94991 3.34961 13.0166V26.9666C3.34961 33.0332 6.9663 36.6499 13.033 36.6499H26.983C33.0496 36.6499 36.6663 33.0332 36.6663 26.9666V13.0166C36.6663 6.94991 33.0496 3.33325 26.983 3.33325Z'
        fill='typography.100'
      />
      <path
        d='M13.6333 27.9501C13.6 27.9501 13.55 27.9501 13.5167 27.9501C11.9 27.8001 10.3833 27.0501 9.25 25.8501C6.58333 23.0501 6.58333 18.5001 9.25 15.7001L12.9 11.8667C14.2 10.5001 15.95 9.7334 17.8167 9.7334C19.6833 9.7334 21.4333 10.4834 22.7333 11.8667C25.4 14.6667 25.4 19.2167 22.7333 22.0167L20.9167 23.9334C20.4333 24.4334 19.65 24.4501 19.15 23.9834C18.65 23.5001 18.6333 22.7167 19.1 22.2167L20.9167 20.3001C22.6833 18.4501 22.6833 15.4334 20.9167 13.6001C19.2667 11.8667 16.3667 11.8667 14.7 13.6001L11.05 17.4334C9.28334 19.2834 9.28334 22.3001 11.05 24.1334C11.7667 24.9001 12.7333 25.3667 13.75 25.4667C14.4333 25.5334 14.9333 26.1501 14.8667 26.8334C14.8167 27.4667 14.2666 27.9501 13.6333 27.9501Z'
        fill='typography.100'
      />
      <path
        d='M22.1833 30.2667C20.3166 30.2667 18.5666 29.5167 17.2666 28.1334C14.5999 25.3334 14.5999 20.7834 17.2666 17.9834L19.0833 16.0668C19.5666 15.5668 20.3499 15.55 20.8499 16.0167C21.3499 16.5 21.3666 17.2834 20.8999 17.7834L19.0833 19.7001C17.3166 21.5501 17.3166 24.5667 19.0833 26.4001C20.7333 28.1334 23.6333 28.1501 25.2999 26.4001L28.9499 22.5668C30.7166 20.7168 30.7166 17.7001 28.9499 15.8667C28.2333 15.1001 27.2666 14.6334 26.2499 14.5334C25.5666 14.4667 25.0666 13.8501 25.1333 13.1667C25.1999 12.4834 25.7999 11.9668 26.4999 12.0501C28.1166 12.2168 29.6333 12.9501 30.7666 14.1501C33.4333 16.9501 33.4333 21.5001 30.7666 24.3001L27.1166 28.1334C25.7999 29.5167 24.0499 30.2667 22.1833 30.2667Z'
        fill='typography.100'
      />
    </>
  ),
  defaultProps: {
    width: '2.5rem',
    height: '2.5rem',
  },
})

export const DotsIcon = createIcon({
  displayName: 'DotsIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <path d='M10.6734 16.2984C11.4422 15.5295 11.4422 14.283 10.6734 13.5141C9.90452 12.7453 8.65797 12.7453 7.88913 13.5141C7.12029 14.283 7.12029 15.5295 7.88913 16.2984C8.65794 17.0672 9.90449 17.0672 10.6734 16.2984Z' />
      <path d='M10.6734 10.3921C11.4422 9.62328 11.4422 8.37673 10.6734 7.60789C9.90452 6.83905 8.65797 6.83905 7.88913 7.60789C7.12029 8.37673 7.12029 9.62328 7.88913 10.3921C8.65794 11.161 9.90449 11.161 10.6734 10.3921Z' />
      <path d='M10.6734 4.48587C11.4422 3.71703 11.4422 2.47048 10.6734 1.70163C9.90452 0.932791 8.65797 0.932791 7.88913 1.70163C7.12029 2.47048 7.12029 3.71702 7.88913 4.48587C8.65794 5.25471 9.90449 5.25471 10.6734 4.48587Z' />
    </>
  ),
  defaultProps: {
    width: '1.125em',
    height: '1.125em',
    fill: 'typography.100',
  },
})

export const TimerIcon = createIcon({
  displayName: 'TimerIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <path
        d='M17.2913 11.0417C17.2913 15.0667 14.0247 18.3333 9.99967 18.3333C5.97467 18.3333 2.70801 15.0667 2.70801 11.0417C2.70801 7.01667 5.97467 3.75 9.99967 3.75C14.0247 3.75 17.2913 7.01667 17.2913 11.0417Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path d='M10 6.66663V10.8333' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' fill='none' />
      <path
        d='M7.5 1.66663H12.5'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
    </>
  ),
  defaultProps: {
    stroke: 'white',
    width: '20px',
    height: '20px',
  },
})

export const DownloadIcon = createIcon({
  displayName: 'DownloadIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <path
        d='M3.33398 13.3337L3.33398 14.167C3.33398 15.5477 4.45327 16.667 5.83398 16.667L14.1673 16.667C15.548 16.667 16.6673 15.5477 16.6673 14.167L16.6673 13.3337M13.334 10.0003L10.0007 13.3337M10.0007 13.3337L6.66732 10.0003M10.0007 13.3337L10.0006 3.33366'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    stroke: 'white',
    fill: 'transparent',
  },
})

export const ExcelFileIcon = createIcon({
  displayName: 'ExcelFileIcon',
  viewBox: '0 0 32 40',
  path: (
    <>
      <path
        d='M0.75 4C0.75 2.20508 2.20508 0.75 4 0.75H20C20.1212 0.75 20.2375 0.798159 20.3232 0.883885L31.1161 11.6768C31.2018 11.7625 31.25 11.8788 31.25 12V36C31.25 37.7949 29.7949 39.25 28 39.25H4C2.20507 39.25 0.75 37.7949 0.75 36V4Z'
        fill='white'
        stroke='#D0D5DD'
        strokeWidth='1.5'
      />
      <path d='M20 0.5V8C20 10.2091 21.7909 12 24 12H31.5' stroke='#D0D5DD' strokeWidth='1.5' />
      <path
        d='M7.8999 24.9H24.0999M7.8999 24.9V21.3C7.8999 20.3059 8.70579 19.5 9.6999 19.5H13.2999M7.8999 24.9V28.5C7.8999 29.4941 8.70579 30.3 9.6999 30.3H13.2999M24.0999 24.9V28.5C24.0999 29.4941 23.294 30.3 22.2999 30.3H13.2999M24.0999 24.9V21.3C24.0999 20.3059 23.294 19.5 22.2999 19.5H13.2999M13.2999 19.5V30.3'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '100%',
    stroke: '#099250',
    fill: 'white',
    strokeWidth: '1',
  },
})

export const DocFileIcon = createIcon({
  displayName: 'WordFileIcon',
  viewBox: '0 0 32 40',
  path: (
    <>
      <path
        d='M0.75 4C0.75 2.20508 2.20508 0.75 4 0.75H20C20.1212 0.75 20.2375 0.798159 20.3232 0.883885L31.1161 11.6768C31.2018 11.7625 31.25 11.8788 31.25 12V36C31.25 37.7949 29.7949 39.25 28 39.25H4C2.20507 39.25 0.75 37.7949 0.75 36V4Z'
        strokeWidth='1.5'
      />
      <path d='M20 0.5V8C20 10.2091 21.7909 12 24 12H31.5' strokeWidth='1.5' />
      <path
        d='M7.90039 19.5H24.1004M7.90039 23.1H24.1004M7.90039 26.7H24.1004M7.90039 30.3H20.5004'
        stroke='#6490E8'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '100%',
    stroke: '#D0D5DD',
    fill: 'white',
    strokeWidth: '1',
  },
})

export const PdfFileIcon = createIcon({
  displayName: 'PdfFileIcon',
  viewBox: '0 0 40 40',
  path: (
    <>
      <path
        d='M7.75 4C7.75 2.20508 9.20508 0.75 11 0.75H27C27.1212 0.75 27.2375 0.798159 27.3232 0.883885L38.1161 11.6768C38.2018 11.7625 38.25 11.8788 38.25 12V36C38.25 37.7949 36.7949 39.25 35 39.25H11C9.20507 39.25 7.75 37.7949 7.75 36V4Z'
        fill='white'
        stroke='#D0D5DD'
        strokeWidth='1.5'
      />
      <path d='M27 0.5V8C27 10.2091 28.7909 12 31 12H38.5' stroke='#D0D5DD' strokeWidth='1.5' fill='white' />
      <rect x='1' y='18' width='26' height='16' rx='2' />
      <path
        d='M4.8323 30V22.7273H7.70162C8.25323 22.7273 8.72316 22.8326 9.11142 23.0433C9.49967 23.2517 9.7956 23.5417 9.9992 23.9134C10.2052 24.2827 10.3082 24.7088 10.3082 25.1918C10.3082 25.6747 10.204 26.1009 9.99565 26.4702C9.78732 26.8395 9.48547 27.1271 9.09011 27.3331C8.69712 27.5391 8.22127 27.642 7.66255 27.642H5.83372V26.4098H7.41397C7.7099 26.4098 7.95375 26.3589 8.14551 26.2571C8.33964 26.1529 8.48405 26.0097 8.57875 25.8274C8.67581 25.6428 8.72434 25.4309 8.72434 25.1918C8.72434 24.9503 8.67581 24.7396 8.57875 24.5597C8.48405 24.3774 8.33964 24.2365 8.14551 24.1371C7.95138 24.0353 7.70517 23.9844 7.40687 23.9844H6.36994V30H4.8323ZM13.885 30H11.3069V22.7273H13.9063C14.6379 22.7273 15.2676 22.8729 15.7955 23.1641C16.3235 23.4529 16.7295 23.8684 17.0136 24.4105C17.3 24.9527 17.4433 25.6013 17.4433 26.3565C17.4433 27.1141 17.3 27.7652 17.0136 28.3097C16.7295 28.8542 16.3211 29.272 15.7884 29.5632C15.2581 29.8544 14.6237 30 13.885 30ZM12.8445 28.6825H13.8211C14.2757 28.6825 14.658 28.602 14.9681 28.4411C15.2806 28.2777 15.515 28.0256 15.6713 27.6847C15.8299 27.3414 15.9092 26.8987 15.9092 26.3565C15.9092 25.8191 15.8299 25.38 15.6713 25.0391C15.515 24.6982 15.2818 24.4472 14.9717 24.2862C14.6615 24.1252 14.2792 24.0447 13.8247 24.0447H12.8445V28.6825ZM18.5823 30V22.7273H23.3976V23.995H20.1199V25.728H23.078V26.9957H20.1199V30H18.5823Z'
        fill='white'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '100%',
    fill: '#D92D20',
    strokeWidth: '1',
  },
})

export const ImageFileIcon = createIcon({
  displayName: 'FileIcon',
  viewBox: '0 0 36 40',
  path: (
    <>
      <path
        d='M4.75 4C4.75 2.20508 6.20508 0.75 8 0.75H24C24.1212 0.75 24.2375 0.798159 24.3232 0.883885L35.1161 11.6768C35.2018 11.7625 35.25 11.8788 35.25 12V36C35.25 37.7949 33.7949 39.25 32 39.25H8C6.20507 39.25 4.75 37.7949 4.75 36V4Z'
        fill='white'
        stroke='#D0D5DD'
      />
      <path d='M24 0.5V8C24 10.2091 25.7909 12 28 12H35.5' stroke='#D0D5DD' />
      <path
        d='M25.2499 30.75H26.0076C26.736 30.75 27.1002 30.75 27.301 30.5981C27.4758 30.4658 27.5841 30.2636 27.5971 30.0447C27.6121 29.7934 27.4101 29.4904 27.0061 28.8844L24.7483 25.4977C24.4145 24.9969 24.2476 24.7465 24.0372 24.6593C23.8532 24.583 23.6465 24.583 23.4626 24.6593C23.2522 24.7465 23.0852 24.9969 22.7514 25.4977L22.1933 26.3349M25.2499 30.75L19.4865 22.4251C19.155 21.9464 18.9893 21.707 18.7823 21.6228C18.6012 21.5492 18.3985 21.5492 18.2174 21.6228C18.0104 21.707 17.8447 21.9464 17.5132 22.4251L13.0535 28.8669C12.6312 29.4769 12.4201 29.7819 12.4321 30.0355C12.4426 30.2564 12.55 30.4614 12.7257 30.5957C12.9274 30.75 13.2983 30.75 14.0401 30.75H25.2499ZM26.7499 19.5C26.7499 20.7426 25.7425 21.75 24.4999 21.75C23.2572 21.75 22.2499 20.7426 22.2499 19.5C22.2499 18.2574 23.2572 17.25 24.4999 17.25C25.7425 17.25 26.7499 18.2574 26.7499 19.5Z'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '100%',
    stroke: '#2365EA',
    fill: 'white',
    strokeWidth: '1',
  },
})

export const VideoFileIcon = createIcon({
  displayName: 'VideoFileIcon',
  viewBox: '0 0 36 40',
  path: (
    <>
      <path
        stroke='#D0D5DD'
        d='M4.75 4C4.75 2.20508 6.20508 0.75 8 0.75H24C24.1212 0.75 24.2375 0.798159 24.3232 0.883885L35.1161 11.6768C35.2018 11.7625 35.25 11.8788 35.25 12V36C35.25 37.7949 33.7949 39.25 32 39.25H8C6.20507 39.25 4.75 37.7949 4.75 36V4Z'
      />
      <path stroke='#D0D5DD' d='M24 0.5V8C24 10.2091 25.7909 12 28 12H35.5' />
      <g clipPath='url(#clip0_27680_116410)'>
        <path
          d='M12.5 24H27.5M12.5 20.25H16.25M23.75 20.25H27.5M12.5 27.75H16.25M23.75 27.75H27.5M16.25 31.5V16.5M23.75 31.5V16.5M16.1 31.5H23.9C25.1601 31.5 25.7902 31.5 26.2715 31.2548C26.6948 31.039 27.039 30.6948 27.2548 30.2715C27.5 29.7902 27.5 29.1601 27.5 27.9V20.1C27.5 18.8399 27.5 18.2098 27.2548 17.7285C27.039 17.3052 26.6948 16.961 26.2715 16.7452C25.7902 16.5 25.1601 16.5 23.9 16.5H16.1C14.8399 16.5 14.2098 16.5 13.7285 16.7452C13.3052 16.961 12.961 17.3052 12.7452 17.7285C12.5 18.2098 12.5 18.8399 12.5 20.1V27.9C12.5 29.1601 12.5 29.7902 12.7452 30.2715C12.961 30.6948 13.3052 31.039 13.7285 31.2548C14.2098 31.5 14.8399 31.5 16.1 31.5Z'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
    </>
  ),
  defaultProps: {
    width: '12px',
    height: '16px',
    stroke: '#155EEF',
    fill: 'white',
    strokeWidth: '1',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const ArrowClockIcon = createIcon({
  displayName: 'ArrowClockIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <g clipPath='url(#clip0_27680_114679)'>
        <path d='M1.78988 14.5344C1.78988 15.1247 1.80253 15.715 1.78566 16.3011C1.7688 16.9125 1.19115 17.313 0.621926 17.1317C0.271961 17.0221 0.00632467 16.7016 0.00632467 16.3348C-0.00210822 14.7958 -0.00210822 13.2568 0.00632467 11.7178C0.0105411 11.2329 0.411103 10.8872 0.938159 10.8829C2.17358 10.8787 3.41321 10.8829 4.64863 10.8829C4.90162 10.8829 5.1546 10.8787 5.40759 10.8872C5.90935 10.8998 6.28883 11.2835 6.29304 11.7726C6.29726 12.2702 5.91778 12.6665 5.40337 12.6707C4.6149 12.6791 3.83064 12.6749 3.04216 12.6749C2.96627 12.6749 2.89037 12.6749 2.78075 12.6749C2.82713 12.7635 2.85242 12.8225 2.89037 12.8773C4.21012 14.8506 6.04849 15.9891 8.42235 16.1661C12.0907 16.4402 15.312 13.9609 16.0204 10.3601C16.8004 6.40508 14.1103 2.55546 10.1174 1.90613C6.18342 1.26523 2.52776 3.88786 1.85313 7.82602C1.82783 7.98624 1.80675 8.14647 1.78145 8.30669C1.70555 8.79158 1.28812 9.12468 0.811666 9.0783C0.335207 9.03614 -0.0274069 8.63136 0.00632467 8.14225C0.0653549 7.27367 0.267744 6.43459 0.613493 5.63768C1.94589 2.54703 4.27337 0.67493 7.58749 0.11836C12.2846 -0.670115 16.8721 2.56811 17.7618 7.2315C18.5882 11.5576 16.324 15.7276 12.2635 17.3594C8.6416 18.8183 4.43781 17.7178 1.96275 14.6693C1.92059 14.6187 1.87421 14.5639 1.83205 14.5133C1.81518 14.5217 1.80253 14.5302 1.78988 14.5344Z' />
        <path d='M8.05353 7.26924C8.05353 6.65785 8.0451 6.04647 8.05775 5.43508C8.06618 4.97549 8.42458 4.62553 8.88839 4.58336C9.3016 4.54541 9.71481 4.84056 9.81601 5.25799C9.84552 5.37184 9.84131 5.49411 9.84131 5.61217C9.84131 6.58196 9.83709 7.55174 9.84974 8.52152C9.84974 8.62693 9.90455 8.75342 9.97623 8.82932C10.4358 9.30578 10.9081 9.76537 11.3719 10.2334C11.6628 10.5285 11.7261 10.9122 11.5616 11.2622C11.4098 11.5911 11.0683 11.8188 10.7141 11.7682C10.5033 11.7387 10.2672 11.6459 10.1196 11.5068C9.49978 10.9249 8.90947 10.3135 8.31074 9.71056C8.12943 9.52503 8.05353 9.30156 8.05775 9.04014C8.05353 8.44984 8.04932 7.85954 8.05353 7.26924Z' />
      </g>
      <defs>
        <clipPath id='clip0_27680_114679'>
          <rect width='17.9325' height='18' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '1.125em',
    height: '1.125em',
    fill: 'white',
  },
})

export const CirclePenIcon = createIcon({
  displayName: 'CirclePenIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <g clipPath='url(#clip0_27680_45238)'>
        <path d='M15.3728 2.64545C13.6761 0.945064 11.4138 0.0036489 9.00913 0C9.00548 0 8.99819 0 8.99454 0C6.59721 0 4.3422 0.934117 2.64546 2.6272C0.945075 4.32759 0.00365955 6.58626 1.06498e-05 8.99088C-0.00363825 11.3919 0.930479 13.6542 2.62722 15.3546C4.32395 17.0549 6.58627 17.9964 8.99089 18C8.99454 18 9.00184 18 9.00548 18C11.4028 18 13.6542 17.0659 15.3546 15.3728C17.0549 13.6761 17.9964 11.4137 18 9.00912C18.0037 6.60815 17.0732 4.34583 15.3728 2.64545ZM16.42 9.00547C16.4164 13.0922 13.0886 16.42 9.00184 16.42C7.02049 16.42 5.15955 15.6428 3.75108 14.2343C2.34625 12.8259 1.57633 10.9649 1.57998 8.98723C1.59093 4.90047 4.91507 1.57997 8.99454 1.57997C8.99819 1.57997 9.00184 1.57997 9.00548 1.57997C13.0995 1.58727 16.4237 4.91871 16.42 9.00547Z' />
        <path d='M11.9328 3.83105H11.9255H11.9182C11.2066 3.842 10.6301 4.07553 10.1631 4.54624L9.14867 5.56063C7.69641 7.01289 6.19307 8.51624 4.71891 9.99404C4.59485 10.1181 4.49998 10.2896 4.46349 10.4502C4.2482 11.3916 4.03657 12.3585 3.86142 13.1941C3.80304 13.4751 3.87602 13.7415 4.06576 13.9348C4.21171 14.0808 4.41605 14.1611 4.63134 14.1611C4.69337 14.1611 4.7554 14.1538 4.81743 14.1428L5.02907 14.099C5.83547 13.9275 6.67107 13.7488 7.49207 13.5554C7.68546 13.5116 7.8898 13.3985 8.03941 13.2562C8.43349 12.8803 8.82027 12.4862 9.19976 12.1067C9.33476 11.9681 9.47342 11.8331 9.60843 11.6944C9.98427 11.3186 10.3601 10.9428 10.7359 10.5669C11.6409 9.66564 12.575 8.73152 13.4909 7.80835C14.1732 7.12236 14.3666 6.11891 13.9835 5.25412C13.593 4.37839 12.8851 3.88944 11.9328 3.83105ZM12.4692 5.75767C12.6443 6.02769 12.6297 6.37798 12.4363 6.60057C12.3305 6.72463 12.2028 6.83774 12.0678 6.95451C12.0568 6.96546 12.0459 6.97275 12.0349 6.9837L11.0023 5.95106C11.0351 5.91457 11.068 5.88173 11.1008 5.84524C11.2833 5.65185 11.4402 5.484 11.6847 5.43292C11.9948 5.37089 12.3013 5.4986 12.4692 5.75767ZM6.91555 12.0593C6.53971 12.1469 6.16752 12.2272 5.76979 12.3147C5.73695 12.322 5.70046 12.3293 5.66398 12.3366C5.67127 12.3001 5.67857 12.2637 5.68952 12.2308C5.76614 11.8951 5.83547 11.5777 5.89386 11.2529C5.9267 11.0705 6.00697 10.9282 6.16022 10.7749C7.2184 9.72402 8.29118 8.65489 9.32382 7.61496L9.84561 7.09317L10.9001 8.15135L10.4185 8.633C9.32747 9.72402 8.19996 10.8552 7.08704 11.9608C7.04326 12.0082 6.97393 12.0484 6.91555 12.0593Z' />
      </g>
      <defs>
        <clipPath id='clip0_27680_45238'>
          <rect width='18' height='18' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '1.125rem',
    height: '1.125rem',
    fill: 'white',
  },
})

export const CircleCheckIcon = createIcon({
  displayName: 'CircleCheckIcon',
  viewBox: '0 0 19 18',
  path: (
    <>
      <g clipPath='url(#clip0_27680_45379)'>
        <path d='M18.4304 3.94671C18.1985 3.35641 17.4691 3.19619 16.9842 3.6347C16.883 3.72746 16.7397 3.85395 16.571 4.00996C16.4445 3.82444 16.3138 3.63891 16.1747 3.46182C15.694 2.85887 15.1585 2.33182 14.5766 1.88066C14.5429 1.84692 14.5049 1.81319 14.4628 1.78368C14.2309 1.61502 13.9905 1.4548 13.746 1.30722C13.7291 1.29879 13.7122 1.28614 13.6996 1.2777C13.6869 1.26927 13.6701 1.26084 13.6574 1.25241C13.6532 1.24819 13.6448 1.24397 13.6406 1.24397C12.2407 0.421766 10.6595 -0.00831122 9.03621 0.000121669C8.5471 0.000121669 8.05799 0.0422861 7.57732 0.122399C6.97015 0.223593 6.3672 0.383818 5.76847 0.60729C2.31098 1.90595 0.000369374 5.32127 0.000369374 8.9685C-0.01228 10.3768 0.299737 11.7935 0.970152 13.0837C2.89285 16.8069 7.01653 18.6368 10.9252 17.7977C12.3883 17.4984 13.7122 16.849 14.8929 15.875C14.9561 15.8202 15.0151 15.7654 15.0657 15.7064C15.8331 15.0233 16.4782 14.2138 16.9969 13.2777C17.1697 12.9657 17.1992 12.6537 17.0179 12.3374C16.8493 12.0381 16.5752 11.8863 16.2379 11.8905C15.85 11.8947 15.5886 12.1013 15.3988 12.4344C14.3869 14.2433 12.8816 15.3944 10.7945 15.9804C9.86263 16.1997 8.85912 16.2334 7.77128 16.0859C7.52672 16.0184 7.11773 15.9298 6.7256 15.7991C2.92237 14.5131 0.898472 10.4358 2.20135 6.64945C2.24352 6.52296 2.29412 6.39647 2.34471 6.27419C2.34893 6.26576 2.35315 6.25311 2.35736 6.24468C2.37001 6.21516 2.38266 6.18565 2.39531 6.15613C3.6223 3.32268 6.62862 1.45058 9.99334 1.86379C10.1494 1.88487 10.3011 1.90595 10.4529 1.93547C11.1613 2.07883 11.8654 2.3276 12.5443 2.69443C12.5991 2.72394 12.6497 2.75346 12.7045 2.78297C12.734 2.79984 12.7678 2.81671 12.7973 2.83779C12.8141 2.85044 12.8352 2.85887 12.8521 2.87152C12.9069 2.90525 12.9617 2.93898 13.0123 2.97271C13.0165 2.97693 13.025 2.98115 13.0292 2.98536C13.0798 3.0191 13.1346 3.05283 13.1852 3.09078C14.0032 3.65578 14.6736 4.37679 15.2133 5.22851C13.7881 6.51874 12.0088 8.13364 11.4312 8.65648C10.7312 9.29738 10.0271 9.93828 9.32293 10.5834C9.25125 10.5244 9.17957 10.4696 9.10789 10.4105C8.69468 10.0774 8.27725 9.74432 7.86404 9.41123C7.21892 8.8926 6.57381 8.37398 5.92447 7.85957C5.57029 7.57707 5.07275 7.6108 4.76917 7.90595C4.44029 8.22641 4.38126 8.70708 4.64689 9.07391C4.727 9.18354 4.84085 9.2763 4.94626 9.36485C5.4185 9.74432 5.89074 10.1238 6.36298 10.5033C7.09243 11.0894 7.81766 11.6755 8.5471 12.2573C8.61456 12.3121 8.68203 12.3712 8.75371 12.4218C9.02778 12.6748 9.41147 12.738 9.75301 12.5651C9.78252 12.5483 9.81625 12.5314 9.84577 12.5103C9.9048 12.4724 9.95539 12.4344 10.0018 12.388C10.0313 12.3627 10.0566 12.3374 10.0861 12.3121C11.7516 10.79 16.2969 6.67897 17.9624 5.15683C18.0552 5.0725 18.1522 4.98818 18.2407 4.89541C18.5063 4.62556 18.5696 4.30089 18.4304 3.94671Z' />
      </g>
      <defs>
        <clipPath id='clip0_27680_45379'>
          <rect width='18.5018' height='18' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '1.1875rem',
    height: '1.125rem',
    fill: 'white',
  },
})

export const SemiCrossIcon = createIcon({
  displayName: 'SemiCrossIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <g clipPath='url(#clip0_27923_88388)'>
        <path d='M15.3886 2.65809C13.691 0.956237 11.4289 0.0126375 9.0151 0C9.00246 0 8.98561 0 8.97297 0C6.59712 0 4.35607 0.922537 2.66686 2.60754C0.965013 4.29675 0.0214138 6.55465 0.00035129 8.96419C-0.0207112 11.3611 0.906038 13.619 2.59946 15.3251C4.29289 17.0311 6.555 17.9789 8.97297 17.9958H9.04037C11.3952 17.9958 13.6236 17.0775 15.3086 15.4135C17.0315 13.7117 17.9877 11.4411 18.0004 9.01896C18.013 6.62205 17.082 4.36415 15.3886 2.65809ZM16.2395 9.01053C16.2353 10.9483 15.4813 12.7639 14.1122 14.1287C12.7474 15.4894 10.9318 16.2392 9.00667 16.2392H8.99403C7.0605 16.235 5.2407 15.4809 3.87585 14.1119C2.511 12.7428 1.76118 10.923 1.76539 8.98947C1.7696 7.05172 2.52364 5.23613 3.8927 3.87128C5.25755 2.51065 7.07313 1.76082 8.99825 1.76082H9.01088C10.9486 1.76504 12.7642 2.51907 14.1291 3.88813C15.4939 5.2572 16.2437 7.07699 16.2395 9.01053Z' />
        <path d='M12.7398 4.8144C12.5292 4.75121 12.209 4.747 11.8931 5.05872C10.5535 6.38987 9.20973 7.71681 7.87016 9.04796L5.11939 11.7734C5.07306 11.8198 5.03514 11.8619 5.00566 11.904C4.76555 12.2326 4.79503 12.6581 5.07727 12.9487C5.23313 13.1088 5.43954 13.1973 5.65859 13.1973C5.82709 13.1973 5.99138 13.1467 6.13039 13.0456C6.21464 12.9824 6.28204 12.9192 6.34102 12.8603L6.36629 12.835C8.56943 10.6529 10.7726 8.46664 12.9757 6.28035C12.9841 6.27192 12.9883 6.26771 12.9968 6.25929L13.001 6.25507C13.0515 6.20874 13.1147 6.14555 13.1737 6.05709C13.2242 5.97284 13.2579 5.88859 13.279 5.83804V5.83382C13.2832 5.8254 13.2832 5.82119 13.2874 5.81697L13.3296 5.72851V5.63162C13.3211 5.23565 13.0894 4.9155 12.7398 4.8144Z' />
      </g>
      <defs>
        <clipPath id='clip0_27923_88388'>
          <rect width='18' height='18' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    fill: 'white',
  },
})

export const MessageQuestionIcon = createIcon({
  displayName: 'MessageQuestionIcon',
  viewBox: '0 0 36 36',
  path: (
    <>
      <path
        opacity='0.4'
        d='M25.5 27.6445H19.5L12.825 32.0846C11.835 32.7446 10.5 32.0395 10.5 30.8395V27.6445C6 27.6445 3 24.6445 3 20.1445V11.1445C3 6.64453 6 3.64453 10.5 3.64453H25.5C30 3.64453 33 6.64453 33 11.1445V20.1445C33 24.6445 30 27.6445 25.5 27.6445Z'
      />
      <path d='M18.0002 18.165C17.3852 18.165 16.8752 17.655 16.8752 17.04V16.7251C16.8752 14.9851 18.1502 14.1301 18.6302 13.8001C19.1852 13.4251 19.3652 13.1701 19.3652 12.7801C19.3652 12.0301 18.7502 11.415 18.0002 11.415C17.2502 11.415 16.6353 12.0301 16.6353 12.7801C16.6353 13.3951 16.1253 13.9051 15.5103 13.9051C14.8953 13.9051 14.3853 13.3951 14.3853 12.7801C14.3853 10.7851 16.0052 9.16504 18.0002 9.16504C19.9952 9.16504 21.6152 10.7851 21.6152 12.7801C21.6152 14.4901 20.3552 15.3451 19.8902 15.6601C19.3052 16.0501 19.1252 16.3051 19.1252 16.7251V17.04C19.1252 17.67 18.6152 18.165 18.0002 18.165Z' />
      <path d='M18 21.9004C17.37 21.9004 16.875 21.3904 16.875 20.7754C16.875 20.1604 17.385 19.6504 18 19.6504C18.615 19.6504 19.125 20.1604 19.125 20.7754C19.125 21.3904 18.63 21.9004 18 21.9004Z' />
    </>
  ),
  defaultProps: {
    width: '2.25rem',
    height: '2.25rem',
    fill: '#4E5D78',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const MuteIcon = createIcon({
  displayName: 'MuteIcon',
  viewBox: '0 0 28 27',
  path: (
    <>
      <path
        d='M21.7772 12.4599C21.7772 13.4405 21.5965 14.3788 21.2665 15.2432M13.9995 20.2531C9.70391 20.2531 6.22168 16.764 6.22168 12.4599M13.9995 20.2531V24.7064M13.9995 20.2531C15.9034 20.2531 17.6475 19.5677 18.9995 18.4296M13.9995 24.7064H9.55501M13.9995 24.7064H18.4439M17.3328 11.3465V5.77991C17.3328 3.9353 15.8404 2.43994 13.9995 2.43994C12.5481 2.43994 11.3134 3.36935 10.8558 4.66659M16.2217 14.9494C15.6319 15.4782 14.8532 15.7998 13.9995 15.7998C12.1585 15.7998 10.6661 14.3045 10.6661 12.4599V9.67655'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M4 2.43994L7.98795 6.43584M24 22.4798L20.0124 18.4843L16.357 14.8216M16.357 14.8216L11.643 10.0981L7.98795 6.43584M16.357 14.8216L7.98795 6.43584'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: 6,
    height: 6,
    stroke: 'white',
    fill: 'none',
  },
})

export const MicIcon = createIcon({
  displayName: 'MicIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      d='M19 11C19 14.866 15.866 18 12 18M12 18C8.13401 18 5 14.866 5 11M12 18V22M12 22H8M12 22H16M12 14C10.3431 14 9 12.6569 9 11V5C9 3.34315 10.3431 2 12 2C13.6569 2 15 3.34315 15 5V11C15 12.6569 13.6569 14 12 14Z'
      stroke='white'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: 6,
    height: 6,
    stroke: 'white',
    fill: 'none',
  },
})

export const MemberIcon = createIcon({
  displayName: 'MemberIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d='M9.16006 10.87C9.06006 10.86 8.94006 10.86 8.83006 10.87C6.45006 10.79 4.56006 8.84 4.56006 6.44C4.56006 3.99 6.54006 2 9.00006 2C11.4501 2 13.4401 3.99 13.4401 6.44C13.4301 8.84 11.5401 10.79 9.16006 10.87Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.41 4C18.35 4 19.91 5.57 19.91 7.5C19.91 9.39 18.41 10.93 16.54 11C16.46 10.99 16.37 10.99 16.28 11'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M4.15997 14.56C1.73997 16.18 1.73997 18.82 4.15997 20.43C6.90997 22.27 11.42 22.27 14.17 20.43C16.59 18.81 16.59 16.17 14.17 14.56C11.43 12.73 6.91997 12.73 4.15997 14.56Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M18.3401 20C19.0601 19.85 19.7401 19.56 20.3001 19.13C21.8601 17.96 21.8601 16.03 20.3001 14.86C19.7501 14.44 19.0801 14.16 18.3701 14'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: 6,
    height: 6,
    stroke: 'white',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const RefreshIcon = createIcon({
  displayName: 'RefreshIcon',
  viewBox: '0 0 19 18',
  path: (
    <path
      d='M17 9C17 13.14 13.64 16.5 9.5 16.5C5.36 16.5 2.8325 12.33 2.8325 12.33M2.8325 12.33H6.2225M2.8325 12.33V16.08M2 9C2 4.86 5.33 1.5 9.5 1.5C14.5025 1.5 17 5.67 17 5.67M17 5.67V1.92M17 5.67H13.67'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
      fill='transparent'
    />
  ),
  defaultProps: {
    width: '1.5rem',
    height: '1.5rem',
    stroke: 'primary.200',
  },
})

export const QuestionMarkIcon = createIcon({
  displayName: 'QuestionMarkIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M7.5763 7.49984C7.77222 6.94289 8.15893 6.47326 8.66793 6.17411C9.17693 5.87497 9.77538 5.76562 10.3573 5.86543C10.9392 5.96524 11.467 6.26777 11.8472 6.71944C12.2274 7.17111 12.4355 7.74277 12.4346 8.33317C12.4346 9.99984 9.93464 10.8332 9.93464 10.8332M10.0013 14.1665H10.0096M18.3346 9.99984C18.3346 14.6022 14.6037 18.3332 10.0013 18.3332C5.39893 18.3332 1.66797 14.6022 1.66797 9.99984C1.66797 5.39746 5.39893 1.6665 10.0013 1.6665C14.6037 1.6665 18.3346 5.39746 18.3346 9.99984Z'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    strokeWidth: '1.75',
    width: '3rem',
    height: '3rem',
    stroke: '#98A2B3',
    fill: 'none',
  },
})

export const ChatIcon = createIcon({
  displayName: 'ChatIcon',
  viewBox: '0 0 20 20',
  path: (
    <g id='vuesax/linear/message-favorite'>
      <g id='message-favorite'>
        <path
          id='Vector'
          d='M18.3327 6.6665V10.8332C18.3327 14.1665 16.666 15.8332 13.3327 15.8332H12.916C12.6577 15.8332 12.4077 15.9582 12.2493 16.1665L10.9993 17.8332C10.4493 18.5665 9.54935 18.5665 8.99935 17.8332L7.74935 16.1665C7.61602 15.9832 7.30768 15.8332 7.08268 15.8332H6.66602C3.33268 15.8332 1.66602 14.9998 1.66602 10.8332V6.6665C1.66602 3.33317 3.33268 1.6665 6.66602 1.6665H9.99935'
          strokeWidth='1.5'
          strokeMiterlimit='10'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          id='Vector_2'
          d='M12.6671 3.85015C12.3921 3.02515 12.7171 2.00848 13.6171 1.72515C14.0838 1.58348 14.6671 1.70015 15.0004 2.14181C15.3171 1.68348 15.9171 1.58348 16.3838 1.72515C17.2838 2.00015 17.6088 3.02515 17.3338 3.85015C16.9088 5.15848 15.4171 5.83348 15.0004 5.83348C14.5838 5.83348 13.1088 5.16681 12.6671 3.85015Z'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path id='Vector_3' d='M13.3301 9.16667H13.3375' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
        <path id='Vector_4' d='M9.99607 9.16667H10.0036' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
        <path id='Vector_5' d='M6.66209 9.16667H6.66957' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
      </g>
    </g>
  ),
  defaultProps: {
    width: '1.041875rem',
    height: '1.041875rem',
    stroke: '#B0B7C3',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const BillIcon = createIcon({
  displayName: 'BillIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      d='M9.35419 21C10.0593 21.6224 10.9856 22 12 22C13.0145 22 13.9407 21.6224 14.6458 21M18 8C18 6.4087 17.3679 4.88258 16.2427 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.8826 2.63214 7.75738 3.75736C6.63216 4.88258 6.00002 6.4087 6.00002 8C6.00002 11.0902 5.22049 13.206 4.34968 14.6054C3.61515 15.7859 3.24788 16.3761 3.26134 16.5408C3.27626 16.7231 3.31488 16.7926 3.46179 16.9016C3.59448 17 4.19261 17 5.38887 17H18.6112C19.8074 17 20.4056 17 20.5382 16.9016C20.6852 16.7926 20.7238 16.7231 20.7387 16.5408C20.7522 16.3761 20.3849 15.7859 19.6504 14.6054C18.7795 13.206 18 11.0902 18 8Z'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '3rem',
    height: '3rem',
    strokeWidth: '1.75',
    stroke: '#B0B7C3',
    fill: 'none',
  },
})

export const WalletIcon = createIcon({
  displayName: 'WalletIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d='M22 12V17C22 20 20 22 17 22H7C4 22 2 20 2 17V12C2 9.28 3.64 7.38 6.19 7.06C6.45 7.02 6.72 7 7 7H17C17.26 7 17.51 7.00999 17.75 7.04999C20.33 7.34999 22 9.26 22 12Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M17.7514 7.05C17.5114 7.01 17.2614 7.00001 17.0014 7.00001H7.00141C6.72141 7.00001 6.45141 7.02001 6.19141 7.06001C6.33141 6.78001 6.53141 6.52001 6.77141 6.28001L10.0214 3.02C11.3914 1.66 13.6114 1.66 14.9814 3.02L16.7314 4.79002C17.3714 5.42002 17.7114 6.22 17.7514 7.05Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M22 12.5H19C17.9 12.5 17 13.4 17 14.5C17 15.6 17.9 16.5 19 16.5H22'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '1.5rem',
    height: '1.5rem',
    stroke: 'white',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const ClockIcon = createIcon({
  displayName: 'ClockIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <g clipPath='url(#clip0_28230_216830)'>
        <path
          d='M9 4.5V9L12 10.5M16.5 9C16.5 13.1421 13.1421 16.5 9 16.5C4.85786 16.5 1.5 13.1421 1.5 9C1.5 4.85786 4.85786 1.5 9 1.5C13.1421 1.5 16.5 4.85786 16.5 9Z'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_28230_216830'>
          <rect width='18' height='18' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '1.125rem',
    height: '1.125rem',
    stroke: '#B0B7C3',
    fill: 'none',
  },
})

export const WalletSquareIcon = createIcon({
  displayName: 'WalletSquareIcon',
  viewBox: '0 0 40 40',
  path: (
    <>
      <rect width='40' height='40' rx='10' fill='white' fillOpacity='0.1' />
      <path d='M21 17H15' stroke='white' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
      <path
        d='M30 18.97V21.03C30 21.58 29.56 22.03 29 22.05H27.0399C25.9599 22.05 24.97 21.26 24.88 20.18C24.82 19.55 25.0599 18.96 25.4799 18.55C25.8499 18.17 26.36 17.95 26.92 17.95H29C29.56 17.97 30 18.42 30 18.97Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M25.48 18.55C25.06 18.96 24.82 19.55 24.88 20.18C24.97 21.26 25.96 22.05 27.04 22.05H29V23.5C29 26.5 27 28.5 24 28.5H15C12 28.5 10 26.5 10 23.5V16.5C10 13.78 11.64 11.88 14.19 11.56C14.45 11.52 14.72 11.5 15 11.5H24C24.26 11.5 24.51 11.51 24.75 11.55C27.33 11.85 29 13.76 29 16.5V17.95H26.92C26.36 17.95 25.85 18.17 25.48 18.55Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '40px',
    // stroke: 'white',
    fill: 'transparent',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const PercentageIcon = createIcon({
  displayName: 'PercentageIcon',
  viewBox: '0 0 40 40',
  path: (
    <svg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect width='40' height='40' rx='10' fill='white' fillOpacity='0.1' />
      <path
        d='M16.5703 23.27L23.1103 16.73'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.98 18.3699C17.6593 18.3699 18.21 17.8192 18.21 17.1399C18.21 16.4606 17.6593 15.9099 16.98 15.9099C16.3007 15.9099 15.75 16.4606 15.75 17.1399C15.75 17.8192 16.3007 18.3699 16.98 18.3699Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.519 24.0899C24.1984 24.0899 24.7491 23.5392 24.7491 22.8599C24.7491 22.1806 24.1984 21.6299 23.519 21.6299C22.8397 21.6299 22.2891 22.1806 22.2891 22.8599C22.2891 23.5392 22.8397 24.0899 23.519 24.0899Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M20 30C25.5228 30 30 25.5228 30 20C30 14.4772 25.5228 10 20 10C14.4772 10 10 14.4772 10 20C10 25.5228 14.4772 30 20 30Z'
        stroke='white'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  defaultProps: {
    width: '40px',
    height: '40px',
    // stroke: 'white',
    fill: 'transparent',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const RyalSquareIcon = createIcon({
  displayName: 'RyalSquareIcon',
  viewBox: '0 0 40 40',
  path: (
    <svg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect width='40' height='40' rx='10' fill='white' fillOpacity='0.1' />
      <g clipPath='url(#clip0_34177_206512)'>
        <path
          d='M21.3366 26.5028C21.0689 27.1223 20.892 27.7947 20.8242 28.4998L26.4887 27.2428C26.7564 26.6234 26.9332 25.9509 27.0011 25.2458L21.3366 26.5028Z'
          fill='white'
        />
        <path
          d='M26.4876 23.4774C26.7553 22.858 26.9322 22.1855 27 21.4804L22.5876 22.4601V20.5769L26.4875 19.7118C26.7551 19.0924 26.9321 18.4199 26.9999 17.7148L22.5874 18.6936V11.9211C21.9113 12.3174 21.3109 12.8449 20.8227 13.4671V19.0852L19.0581 19.4768V11C18.3819 11.3961 17.7815 11.9238 17.2934 12.546V19.8682L13.3449 20.744C13.0772 21.3634 12.9002 22.0359 12.8322 22.741L17.2934 21.7514V24.1229L12.5124 25.1834C12.2447 25.8028 12.0678 26.4753 12 27.1804L17.0044 26.0703C17.4117 25.9818 17.7619 25.7304 17.9895 25.3844L18.9073 23.9641C19.0026 23.8171 19.0581 23.6397 19.0581 23.4489V21.3598L20.8227 20.9683V24.7346L26.4876 23.4774Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_34177_206512'>
          <rect width='15' height='17.5' fill='white' transform='translate(12 11)' />
        </clipPath>
      </defs>
    </svg>
  ),
  defaultProps: {
    width: '40px',
    height: '40px',
    fill: 'transparent',
  },
})

export const MoneySquareIcon = createIcon({
  displayName: 'MoneySquareIcon',
  viewBox: '0 0 40 40',
  path: (
    <>
      <rect width='40' height='40' rx='10' fill='white' fillOpacity='0.1' />
      <path
        d='M25 28.5H15C12 28.5 10 27 10 23.5V16.5C10 13 12 11.5 15 11.5H25C28 11.5 30 13 30 16.5V23.5C30 27 28 28.5 25 28.5Z'
        stroke='white'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M20 23C21.6569 23 23 21.6569 23 20C23 18.3431 21.6569 17 20 17C18.3431 17 17 18.3431 17 20C17 21.6569 18.3431 23 20 23Z'
        stroke='white'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M13.5 17.5V22.5'
        stroke='white'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M26.5 17.5V22.5'
        stroke='white'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '40px',
    height: '40px',
    // stroke: 'white',
    fill: 'transparent',
  },
})

export const PlayStoreCircleIcon = createIcon({
  displayName: 'PlayStoreCircleIcon',
  viewBox: '0 0 39 39',
  path: (
    <>
      <foreignObject x='-11.3846' y='-11.3846' width='61.7692' height='61.7692'>
        <div
          style={{
            backdropFilter: 'blur(5.69px)',
            clipPath: 'url(#bgblur_0_28160_86381_clip_path)',
            height: '100%',
            width: '100%',
          }}></div>
      </foreignObject>
      <circle data-figma-bg-blur-radius='11.3846' cx='19.5' cy='19.5' r='19.5' fill='white' fillOpacity='0.08' />
      <path d='M20.3271 19.4646L13 26.6954V12.2338L20.3271 19.4646Z' fill='white' />
      <path
        d='M24.7167 23.7963L16.2246 28.6329C16.0691 28.723 15.9025 28.7934 15.7284 28.8427C14.7683 29.113 13.776 28.6885 13.291 27.869L21.0673 20.1951L24.7167 23.7963Z'
        fill='white'
      />
      <path
        d='M24.7051 15.1451L21.0669 18.7352L13.2881 11.0589C13.6682 10.4134 14.3692 9.99877 15.1467 10C15.5354 10.0012 15.9168 10.1061 16.2525 10.3024L24.7039 15.1451H24.7051Z'
        fill='white'
      />
      <path
        d='M29.0249 21.3455L25.6606 23.2658L21.8076 19.4647L25.6421 15.6808L29.036 17.6246H29.0236C29.4025 17.8232 29.711 18.1318 29.9097 18.5107C30.4478 19.5375 30.0504 20.8074 29.0236 21.3455H29.0249Z'
        fill='white'
      />
      <defs>
        <clipPath id='bgblur_0_28160_86381_clip_path'>
          <circle transform='translate(11.3846 11.3846)' cx='19.5' cy='19.5' r='19.5' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '39px',
    height: '39px',
    // stroke: 'white',
    fill: 'transparent',
  },
})

export const AppStoreCircleIcon = createIcon({
  displayName: 'AppStoreCircleIcon',
  viewBox: '0 0 39 39',
  path: (
    <>
      <foreignObject x='-11.3846' y='-11.3846' width='61.7692' height='61.7692'>
        <div className='blurred-background' data-clip-path='bgblur_0_28160_86372_clip_path'></div>
      </foreignObject>
      <circle data-figma-bg-blur-radius='11.3846' cx='19.5' cy='19.5' r='19.5' fill='white' fillOpacity='0.08' />
      <foreignObject x='33.6154' y='-11.3846' width='61.7692' height='61.7692'>
        <div className='blurred-background' data-clip-path='bgblur_1_28160_86372_clip_path'></div>
      </foreignObject>
      <circle data-figma-bg-blur-radius='11.3846' cx='64.5' cy='19.5' r='19.5' fill='white' fillOpacity='0.08' />
      <path d='M65.3271 19.4646L58 26.6954V12.2338L65.3271 19.4646Z' fill='white' />
      <path
        d='M69.7167 23.7963L61.2246 28.6329C61.0691 28.723 60.9025 28.7934 60.7284 28.8427C59.7683 29.113 58.776 28.6885 58.291 27.869L66.0673 20.1951L69.7167 23.7963Z'
        fill='white'
      />
      <path
        d='M69.7051 15.1451L66.0669 18.7352L58.2881 11.0589C58.6682 10.4134 59.3692 9.99877 60.1467 10C60.5354 10.0012 60.9168 10.1061 61.2525 10.3024L69.7039 15.1451H69.7051Z'
        fill='white'
      />
      <path
        d='M74.0249 21.3455L70.6606 23.2658L66.8076 19.4647L70.6421 15.6808L74.036 17.6246H74.0236C74.4025 17.8232 74.711 18.1318 74.9097 18.5107C75.4478 19.5375 75.0504 20.8074 74.0236 21.3455H74.0249Z'
        fill='white'
      />
      <path
        d='M18.7698 10.9909C18.6168 9.75914 19.2275 8.4531 19.908 7.65075C20.6806 6.74143 22.0253 6.04457 23.0951 6C23.2333 7.28375 22.7281 8.54522 21.9867 9.47089C21.2155 10.3802 19.9763 11.083 18.7698 10.9924V10.9909Z'
        fill='white'
      />
      <path
        d='M27.6058 21.8836C27.591 21.9594 27.1631 23.4512 26.1007 24.9563C25.2062 26.2876 24.2806 27.5862 22.7977 27.6085C21.3609 27.6382 20.8795 26.7601 19.2273 26.7601C17.575 26.7601 17.049 27.5862 15.6732 27.6382C14.2587 27.6917 13.1903 26.2178 12.2647 24.9028C10.4222 22.212 8.99288 17.3192 10.9111 13.9939C11.8368 12.3595 13.541 11.3105 15.3522 11.2808C16.7578 11.2585 18.0728 12.2361 18.9375 12.2361C19.8023 12.2361 21.4055 11.0579 23.0726 11.2347C23.7694 11.2585 25.756 11.517 27.0397 13.3832C26.9402 13.4441 24.6773 14.7754 24.701 17.5108C24.7308 20.7826 27.5672 21.8688 27.6058 21.8836Z'
        fill='white'
      />
      <defs>
        <clipPath id='bgblur_0_28160_86372_clip_path'>
          <circle transform='translate(11.3846 11.3846)' cx='19.5' cy='19.5' r='19.5' />
        </clipPath>
        <clipPath id='bgblur_1_28160_86372_clip_path'>
          <circle transform='translate(-33.6154 11.3846)' cx='64.5' cy='19.5' r='19.5' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '39px',
    height: '39px',
    fill: 'transparent',
  },
})

export const AppStoreIcon = createIcon({
  displayName: 'AppStoreIcon',
  viewBox: '0 0 37 45',
  path: (
    <>
      <path
        d='M18.091 10.2955C17.7753 7.75459 19.035 5.06041 20.4388 3.40528C22.0326 1.52946 24.8065 0.0919517 27.0134 0C27.2984 2.64821 26.2563 5.25044 24.7268 7.15997C23.1361 9.03579 20.5798 10.4856 18.091 10.2986V10.2955Z'
        fill='white'
      />
      <path
        d='M36.3184 32.7657C36.2878 32.922 35.405 35.9994 33.2135 39.1043C31.3683 41.8506 29.4588 44.5294 26.3999 44.5754C23.436 44.6367 22.4429 42.8252 19.0346 42.8252C15.6262 42.8252 14.5412 44.5294 11.7029 44.6367C8.78501 44.747 6.58123 41.7065 4.6717 38.9939C0.871033 33.4431 -2.07755 23.3499 1.87944 16.4903C3.78897 13.1187 7.30459 10.9548 11.0409 10.8935C13.9404 10.8475 16.653 12.8643 18.4369 12.8643C20.2207 12.8643 23.5279 10.4337 26.9669 10.7985C28.4044 10.8475 32.5024 11.3808 35.1506 15.2305C34.9453 15.3562 30.2772 18.1025 30.3262 23.7453C30.3875 30.4945 36.2387 32.7351 36.3184 32.7657Z'
        fill='white'
      />
    </>
  ),
  defaultProps: {
    width: '37px',
    height: '45px',
  },
})

export const PlayStoreIcon = createIcon({
  displayName: 'PlayStoreIcon',
  viewBox: '0 0 35 39',
  path: (
    <>
      <path d='M15.1084 19.2298L0.424805 33.7205V4.73914L15.1084 19.2298Z' fill='white' />
      <path
        d='M23.906 27.9108L6.88766 37.6034C6.57604 37.7839 6.24215 37.9249 5.89343 38.0239C3.96925 38.5655 1.98077 37.7147 1.00879 36.0725L16.5926 20.6939L23.906 27.9108Z'
        fill='white'
      />
      <path
        d='M23.8828 10.5735L16.5917 17.7682L1.00293 2.38467C1.76469 1.09117 3.16948 0.260167 4.72762 0.26264C5.50669 0.265113 6.27092 0.475338 6.94364 0.868582L23.8803 10.5735H23.8828Z'
        fill='white'
      />
      <path
        d='M32.5396 22.9991L25.7976 26.8475L18.0762 19.2299L25.7605 11.647L32.5619 15.5423H32.5372C33.2965 15.9405 33.9148 16.5588 34.3129 17.3181C35.3913 19.3758 34.5949 21.9208 32.5372 22.9991H32.5396Z'
        fill='white'
      />
    </>
  ),
  defaultProps: {
    width: '35px',
    height: '39px',
  },
})

export const SmallSellSign = createIcon({
  displayName: 'SmallSellSign',
  viewBox: '0 0 16 16',
  path: (
    <>
      <path
        d='M6.33301 9.1667C6.33301 9.81336 6.83302 10.3334 7.44635 10.3334H8.69967C9.233 10.3334 9.66634 9.88003 9.66634 9.31336C9.66634 8.7067 9.39968 8.4867 9.00635 8.3467L6.99967 7.6467C6.60634 7.5067 6.33968 7.29337 6.33968 6.68003C6.33968 6.12003 6.773 5.66003 7.30634 5.66003H8.55967C9.17301 5.66003 9.67301 6.18003 9.67301 6.8267'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path d='M8 5V11' stroke='#292D32' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
      <path
        d='M14.6663 7.99998C14.6663 11.68 11.6797 14.6666 7.99967 14.6666C4.31967 14.6666 1.33301 11.68 1.33301 7.99998C1.33301 4.31998 4.31967 1.33331 7.99967 1.33331'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.333 2V4.66667H13.9997'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14.6663 1.33331L11.333 4.66665'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '16px',
    height: '16px',
    fill: 'transparent',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const SmallGiftrSign = createIcon({
  displayName: 'SmallGiftrSign',
  viewBox: '0 0 16 16',
  path: (
    <>
      <path
        d='M13.3132 6.66669H2.64648V12C2.64648 14 3.31315 14.6667 5.31315 14.6667H10.6465C12.6465 14.6667 13.3132 14 13.3132 12V6.66669Z'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14.3337 4.66665V5.33331C14.3337 6.06665 13.9803 6.66665 13.0003 6.66665H3.00033C1.98033 6.66665 1.66699 6.06665 1.66699 5.33331V4.66665C1.66699 3.93331 1.98033 3.33331 3.00033 3.33331H13.0003C13.9803 3.33331 14.3337 3.93331 14.3337 4.66665Z'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.76018 3.33336H4.08018C3.85352 3.08669 3.86018 2.70669 4.10018 2.46669L5.04685 1.52002C5.29352 1.27336 5.70018 1.27336 5.94685 1.52002L7.76018 3.33336Z'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.9134 3.33336H8.2334L10.0467 1.52002C10.2934 1.27336 10.7001 1.27336 10.9467 1.52002L11.8934 2.46669C12.1334 2.70669 12.1401 3.08669 11.9134 3.33336Z'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M5.95996 6.66669V10.0934C5.95996 10.6267 6.54663 10.94 6.99329 10.6534L7.61996 10.24C7.84663 10.0934 8.13329 10.0934 8.35329 10.24L8.94663 10.64C9.38663 10.9334 9.97996 10.62 9.97996 10.0867V6.66669H5.95996Z'
        stroke='#292D32'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '16px',
    height: '16px',
    fill: 'transparent',
  },
})

export const RiyalNewSignIcon = createIcon({
  displayName: 'RiyalNewSignIcon',
  viewBox: '0 0 10 12',
  path: (
    <>
      <path d='M6.22441 10.6305C6.04596 11.0553 5.928 11.5163 5.88281 11.9998L9.65912 11.1379C9.83757 10.7132 9.95544 10.252 10.0007 9.76855L6.22441 10.6305Z' />
      <path d='M9.6584 8.55594C9.83685 8.1312 9.95481 7.67007 10 7.18659L7.05837 7.85832V6.56701L9.65832 5.97378C9.83676 5.54904 9.95472 5.08791 9.99991 4.60443L7.05828 5.27559V0.631619C6.60754 0.90335 6.20723 1.26505 5.88183 1.6917V5.54417L4.70537 5.81265V0C4.25463 0.271635 3.85432 0.633434 3.52892 1.06008V6.08104L0.896596 6.68162C0.718149 7.10636 0.600103 7.56749 0.554824 8.05097L3.52892 7.37236V8.99854L0.341594 9.72577C0.163147 10.1505 0.0451901 10.6116 0 11.0951L3.33624 10.3339C3.60782 10.2732 3.84125 10.1008 3.99301 9.86359L4.60485 8.88966C4.66837 8.78889 4.70537 8.66721 4.70537 8.53636V7.10388L5.88183 6.83539V9.41803L9.6584 8.55594Z' />
    </>
  ),
  defaultProps: {
    width: '12px',
    height: '14px',
    fill: 'typography.100',
    pb: '0.1rem',
  },
})

export const SimpleArrowIcon = createIcon({
  displayName: 'SimpleArrowIcon',
  viewBox: '0 0 20 20',
  path: (
    <>
      <path d='M2 10L18 10M18 10L12 4M18 10L12 16' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    stroke: 'typography.100',
    _rtl: {
      transform: 'scaleX(-1)',
    },
  },
})

export const SmallClockIcon = createIcon({
  displayName: 'SmallClockIcon',
  viewBox: '0 0 18 18',
  path: (
    <path
      d='M9 4.5V9L12 10.5M16.5 9C16.5 13.1421 13.1421 16.5 9 16.5C4.85786 16.5 1.5 13.1421 1.5 9C1.5 4.85786 4.85786 1.5 9 1.5C13.1421 1.5 16.5 4.85786 16.5 9Z'
      stroke='#8A94A6'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    stroke: '#8A94A6',
    fill: 'transparent',
    my: 'auto',
  },
})

export const VideoIcon = createIcon({
  displayName: 'VideoIcon',
  viewBox: '0 0 25 24',
  path: (
    <path
      d='M15.5 10L20.0528 7.72361C20.7177 7.39116 21.5 7.87465 21.5 8.61803V15.382C21.5 16.1253 20.7177 16.6088 20.0528 16.2764L15.5 14M5.5 18H13.5C14.6046 18 15.5 17.1046 15.5 16V8C15.5 6.89543 14.6046 6 13.5 6H5.5C4.39543 6 3.5 6.89543 3.5 8V16C3.5 17.1046 4.39543 18 5.5 18Z'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '25px',
    height: '24px',
    fill: 'transparent',
    stroke: '#8A94A6',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const PlusIcon = createIcon({
  displayName: 'PlusIcon',
  viewBox: '0 0 18 18',
  path: (
    <path d='M9 4.5V9M9 9V13.5M9 9H13.5M9 9L4.5 9' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    stroke: '#FFFFFF',
  },
})

export const LockIcon = createIcon({
  displayName: 'LockIcon',
  viewBox: '0 0 19 18',
  path: (
    <path
      d='M9.5 11.25V12.75M5 15.75H14C14.8284 15.75 15.5 15.0784 15.5 14.25V9.75C15.5 8.92157 14.8284 8.25 14 8.25H5C4.17157 8.25 3.5 8.92157 3.5 9.75V14.25C3.5 15.0784 4.17157 15.75 5 15.75ZM12.5 8.25V5.25C12.5 3.59315 11.1569 2.25 9.5 2.25C7.84315 2.25 6.5 3.59315 6.5 5.25V8.25H12.5Z'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '1.8rem',
    height: '1.8rem',
    stroke: 'white',
    fill: 'transparent',
  },
})

export const SearchIcon = createIcon({
  displayName: 'SearchIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      d='M20.5 20.5L16 16M18 11C18 14.866 14.866 18 11 18C7.13401 18 4 14.866 4 11C4 7.13401 7.13401 4 11 4C14.866 4 18 7.13401 18 11Z'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
    fill: 'transparent',
    stroke: '#B0B7C3',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CheckIcon = createIcon({
  displayName: 'CheckIcon',
  viewBox: '0 0 16 12',
  path: <path d='M1 7L5 11L15 1' strokeWidth='2.5' strokeLinecap='round' strokeLinejoin='round' />,
  defaultProps: {
    width: '16px',
    height: '12px',
    stroke: 'rgb(106, 106, 241)',
    fill: 'transparent',
  },
})

export const ArrowRightIcon = createIcon({
  displayName: 'ArrowRightIcon',
  viewBox: '0 0 8 12',
  path: <path d='M1.5 11L6.5 6L1.5 1' strokeWidth='1.67' strokeLinecap='round' strokeLinejoin='round' />,
  defaultProps: {
    width: '8px',
    height: '12px',
    fill: 'transparent',
    stroke: 'typography.100',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const ArrowLeftIcon = createIcon({
  displayName: 'ArrowLeftIcon',
  viewBox: '0 0 12 10',
  path: (
    <path
      d='M6.75 1.25L10.5 5M10.5 5L6.75 8.75M10.5 5L1.5 5'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '12px',
    height: '10px',
    fill: 'transparent',
    stroke: 'typography.100',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const GreenCheckIcon = createIcon({
  displayName: 'GreenCheckIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M16.748 7H11.918C11.508 7 11.168 7.34 11.168 7.75C11.168 8.16 11.508 8.5 11.918 8.5H14.938L7.21805 16.22C6.92805 16.51 6.92805 16.99 7.21805 17.28C7.36805 17.43 7.55805 17.5 7.74805 17.5C7.93805 17.5 8.12805 17.43 8.27805 17.28L15.998 9.56V12.58C15.998 12.99 16.338 13.33 16.748 13.33C17.158 13.33 17.498 12.99 17.498 12.58V7.75C17.498 7.34 17.158 7 16.748 7Z'
      fill='#04C200'
    />
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    fill: 'transparent',
  },
})

export const RedCheckIcon = createIcon({
  displayName: 'RedCheckIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M7.75 17.5H12.58C12.99 17.5 13.33 17.16 13.33 16.75C13.33 16.34 12.99 16 12.58 16H9.56L17.28 8.28C17.57 7.99 17.57 7.51 17.28 7.22C17.13 7.07 16.94 7 16.75 7C16.56 7 16.37 7.07 16.22 7.22L8.5 14.94V11.92C8.5 11.51 8.16 11.17 7.75 11.17C7.34 11.17 7 11.51 7 11.92V16.75C7 17.16 7.34 17.5 7.75 17.5Z'
      fill='#FF5630'
    />
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    fill: 'transparent',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const TwoLinesIcon = createIcon({
  displayName: 'TwoLinesIcon',
  viewBox: '0 0 24 24',
  path: <path d='M5 9H19M5 15H19' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />,
  defaultProps: {
    width: '24px',
    height: '24px',
    stroke: '#5F58FF',
  },
})

export const SingleLineIcon = createIcon({
  displayName: 'SingleLineIcon',
  viewBox: '0 0 24 24',
  path: <path d='M18 12H6' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />,
  defaultProps: {
    width: '24px',
    height: '24px',
    fill: 'transparent',
    stroke: '#5F58FF',
  },
})

export const EditIcon = createIcon({
  displayName: 'EditIcon',
  viewBox: '0 0 16 16',
  path: (
    <>
      <path
        d='M8.83958 2.4008L3.36624 8.19413C3.15958 8.41413 2.95958 8.84746 2.91958 9.14746L2.67291 11.3075C2.58624 12.0875 3.14624 12.6208 3.91958 12.4875L6.06624 12.1208C6.36624 12.0675 6.78624 11.8475 6.99291 11.6208L12.4662 5.82746C13.4129 4.82746 13.8396 3.68746 12.3662 2.29413C10.8996 0.914129 9.78624 1.4008 8.83958 2.4008Z'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.92969 3.36719C8.21635 5.20719 9.70969 6.61385 11.563 6.80052'
        strokeWidth='1.5'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path d='M2 14.666H14' strokeWidth='1.5' strokeMiterlimit='10' strokeLinecap='round' strokeLinejoin='round' />
    </>
  ),
  defaultProps: {
    width: '16px',
    height: '16px',
    fill: 'transparent',
    stroke: '#00BFB2',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const FilterIcon = createIcon({
  displayName: 'FilterIcon',
  viewBox: '0 0 34 34',
  path: (
    <>
      <rect x='0.75' y='0.75' width='32.5' height='32.5' rx='9.25' />
      <g>
        <path d='M9.01217 21.1857C9.07496 20.7995 9.37009 20.4541 9.90385 20.4541C11.0404 20.451 12.1739 20.4541 13.3104 20.4541C14.3717 20.4541 15.4329 20.451 16.4972 20.4573C16.6228 20.4573 16.6762 20.4227 16.717 20.3003C17.1691 18.9628 18.4156 18.0711 19.8159 18.0742C21.2131 18.0774 22.4564 18.9753 22.9054 20.316C22.9431 20.4259 22.9902 20.4604 23.1001 20.4573C23.4297 20.451 23.7594 20.4541 24.0891 20.4541C25.301 20.4604 25.3073 22.2375 24.064 22.2406C23.7406 22.2406 23.414 22.2469 23.0906 22.2375C22.9839 22.2344 22.9399 22.2689 22.9054 22.3725C22.4533 23.7195 21.2225 24.608 19.8159 24.6111C18.4093 24.6143 17.1723 23.732 16.7139 22.3882C16.6731 22.2626 16.6134 22.2375 16.491 22.2375C15.794 22.2438 15.1001 22.2406 14.4031 22.2406C12.9054 22.2406 11.4109 22.2406 9.91326 22.2406C9.23509 22.2375 8.93367 21.6786 9.01217 21.1857ZM21.2885 21.3458C21.2916 20.5326 20.6291 19.8639 19.8159 19.8607C18.9965 19.8545 18.3246 20.5232 18.3214 21.3427C18.3214 22.1622 18.9933 22.8341 19.8096 22.8278C20.626 22.8215 21.2853 22.1622 21.2885 21.3458Z' />
        <path d='M24.9934 13.7575C24.9463 14.1437 24.6668 14.4859 24.1362 14.4859C22.9965 14.4859 21.8568 14.4859 20.7139 14.4859C19.6402 14.4859 18.5695 14.4859 17.4957 14.4827C17.3858 14.4827 17.3356 14.5079 17.2948 14.6209C16.8364 15.9741 15.5962 16.8658 14.1927 16.8658C12.7893 16.8658 11.5554 15.9678 11.097 14.6115C11.0656 14.5173 11.0279 14.4796 10.9243 14.4827C10.5883 14.489 10.2555 14.4859 9.91959 14.4859C8.70765 14.4796 8.71393 12.7057 9.92901 12.6994C10.2587 12.6994 10.5883 12.6962 10.918 12.7025C11.0153 12.7057 11.0624 12.6805 11.097 12.5801C11.5648 11.2174 12.783 10.3352 14.1927 10.332C15.6056 10.3289 16.8332 11.2112 17.2979 12.5707C17.3325 12.6743 17.3764 12.7057 17.4832 12.7057C18.086 12.6994 18.6857 12.7025 19.2885 12.7025C20.8835 12.7025 22.4816 12.7025 24.0766 12.7025C24.7328 12.7025 25.053 13.2645 24.9934 13.7575ZM12.7171 13.6162C12.7359 14.4482 13.4141 15.1013 14.243 15.0824C15.053 15.0605 15.6998 14.3791 15.6841 13.5659C15.6684 12.7559 14.9777 12.0934 14.1739 12.1185C13.3482 12.1405 12.6982 12.8093 12.7171 13.6162Z' />
      </g>
    </>
  ),
  defaultProps: {
    width: '34px',
    height: '34px',
    fill: 'transparent',
    stroke: '#00BFB2',
    strokeWidth: '1',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const Search2Icon = createIcon({
  displayName: 'SearchIcon',
  viewBox: '0 0 34 34',
  path: (
    <>
      <rect x='0.75' y='0.75' width='32.5' height='32.5' rx='9.25' strokeWidth='1.5' />
      <path
        d='M23.375 23.375L20 20M21.5 16.25C21.5 19.1495 19.1495 21.5 16.25 21.5C13.3505 21.5 11 19.1495 11 16.25C11 13.3505 13.3505 11 16.25 11C19.1495 11 21.5 13.3505 21.5 16.25Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </>
  ),
  defaultProps: {
    width: '34px',
    height: '34px',
    fill: 'transparent',
    stroke: '#00BFB2',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const LocationIcon = createIcon({
  displayName: 'LocationIcon',
  viewBox: '0 0 20 21',
  path: (
    <>
      <path
        d='M3.47483 13.4616L7.24983 17.2366C8.79983 18.7866 11.3165 18.7866 12.8748 17.2366L16.5332 13.5783C18.0832 12.0283 18.0832 9.51159 16.5332 7.95325L12.7498 4.18659C11.9582 3.39492 10.8665 2.96992 9.74983 3.02826L5.58316 3.22826C3.9165 3.30326 2.5915 4.62826 2.50816 6.28659L2.30816 10.4533C2.25816 11.5783 2.68316 12.6699 3.47483 13.4616Z'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M7.91634 10.7116C9.06693 10.7116 9.99967 9.77885 9.99967 8.62825C9.99967 7.47766 9.06693 6.54492 7.91634 6.54492C6.76575 6.54492 5.83301 7.47766 5.83301 8.62825C5.83301 9.77885 6.76575 10.7116 7.91634 10.7116Z'
        strokeLinecap='round'
        strokeWidth='1.5'
      />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '21px',
    fill: 'transparent',
    stroke: '#8A94A6',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const Wallet2Icon = createIcon({
  displayName: 'WalletIcon',
  viewBox: '0 0 20 21',
  path: (
    <>
      <path
        d='M16.0831 7.31117V11.6029C16.0831 14.1695 14.6165 15.2695 12.4165 15.2695H5.09147C4.71647 15.2695 4.35813 15.2362 4.0248 15.1612C3.81647 15.1279 3.61647 15.0695 3.43314 15.0029C2.18314 14.5362 1.4248 13.4529 1.4248 11.6029V7.31117C1.4248 4.7445 2.89147 3.64453 5.09147 3.64453H12.4165C14.2831 3.64453 15.6248 4.43619 15.9831 6.24453C16.0415 6.57786 16.0831 6.9195 16.0831 7.31117Z'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M18.5842 9.81127V14.103C18.5842 16.6696 17.1176 17.7696 14.9176 17.7696H7.59254C6.97588 17.7696 6.41755 17.6863 5.93422 17.503C4.94255 17.1363 4.26755 16.378 4.02588 15.1613C4.35921 15.2363 4.71754 15.2696 5.09254 15.2696H12.4176C14.6176 15.2696 16.0842 14.1696 16.0842 11.603V7.31127C16.0842 6.9196 16.0509 6.56963 15.9842 6.24463C17.5676 6.57796 18.5842 7.6946 18.5842 9.81127Z'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M8.74835 11.6613C9.96338 11.6613 10.9484 10.6763 10.9484 9.46124C10.9484 8.24622 9.96338 7.26123 8.74835 7.26123C7.53333 7.26123 6.54834 8.24622 6.54834 9.46124C6.54834 10.6763 7.53333 11.6613 8.74835 11.6613Z'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path d='M3.98291 7.62793V11.2946' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
      <path d='M13.5181 7.62793V11.2946' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '21px',
    stroke: '#8A94A6',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CustomMoneyIcon = createIcon({
  displayName: 'CustomMoneyIcon',
  viewBox: '0 0 20 21',
  path: (
    <>
      <path
        d='M6.6665 10.2114C6.6665 10.8531 7.1665 11.3781 7.77484 11.3781H9.02484C9.55817 11.3781 9.9915 10.9198 9.9915 10.3614C9.9915 9.75309 9.72484 9.53643 9.33317 9.39476L7.33317 8.69476C6.93317 8.55309 6.6665 8.33643 6.6665 7.72809C6.6665 7.16976 7.09984 6.71143 7.63317 6.71143H8.88317C9.49984 6.71976 9.99984 7.23643 9.99984 7.87809'
        fill='#8A94A6'
      />
      <path
        d='M6.6665 10.2114C6.6665 10.8531 7.1665 11.3781 7.77484 11.3781H9.02484C9.55817 11.3781 9.9915 10.9198 9.9915 10.3614C9.9915 9.75309 9.72484 9.53643 9.33317 9.39476L7.33317 8.69476C6.93317 8.55309 6.6665 8.33643 6.6665 7.72809C6.6665 7.16976 7.09984 6.71143 7.63317 6.71143H8.88317C9.49984 6.71976 9.99984 7.23643 9.99984 7.87809'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path d='M8.3335 11.4199V12.0366' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
      <path d='M8.3335 6.05322V6.70322' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
      <path
        d='M8.32484 15.6946C12.0021 15.6946 14.9832 12.7136 14.9832 9.03626C14.9832 5.35897 12.0021 2.37793 8.32484 2.37793C4.64754 2.37793 1.6665 5.35897 1.6665 9.03626C1.6665 12.7136 4.64754 15.6946 8.32484 15.6946Z'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
      <path
        d='M10.8169 17.2783C11.5669 18.3366 12.7919 19.0283 14.1919 19.0283C16.4669 19.0283 18.3169 17.1783 18.3169 14.9033C18.3169 13.5199 17.6336 12.2949 16.5919 11.5449'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
      />
    </>
  ),
  defaultProps: {
    width: '20px',
    height: '21px',
    fill: 'transparent',
    stroke: '#8A94A6',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CustomCircleMinusIcon = createIcon({
  displayName: 'CustomCircleMinusIcon',
  viewBox: '0 0 25 25',
  path: (
    <path
      d='M15.5 12.5H9.5M21.5 12.5C21.5 17.4706 17.4706 21.5 12.5 21.5C7.52944 21.5 3.5 17.4706 3.5 12.5C3.5 7.52944 7.52944 3.5 12.5 3.5C17.4706 3.5 21.5 7.52944 21.5 12.5Z'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '25px',
    height: '25px',
    stroke: '#FF5630',
    fill: 'transparent',
  },
})

export const DocumentTextIcon = createIcon({
  displayName: 'DocumentTextIcon',
  viewBox: '0 0 18 18',
  path: (
    <>
      <path
        d='M16.5 7.5V11.25C16.5 15 15 16.5 11.25 16.5H6.75C3 16.5 1.5 15 1.5 11.25V6.75C1.5 3 3 1.5 6.75 1.5H10.5'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.5 7.5H13.5C11.25 7.5 10.5 6.75 10.5 4.5V1.5L16.5 7.5Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path d='M5.25 9.75H9.75' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
      <path d='M5.25 12.75H8.25' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
    </>
  ),
  defaultProps: {
    width: '25.6px',
    height: '25.6px',
    stroke: '#00BFB2',
    fill: 'transparent',
  },
})

export const QuickActionsIcon = createIcon({
  displayName: 'QuickActionsIcon',
  viewBox: '0 0 18 18',
  path: (
    <svg viewBox='0 0 25 37' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M12.6961 14.2633C12.5935 14.8733 13.0637 15.4291 13.6822 15.4291H23.1822C23.9705 15.4291 24.4489 16.2987 24.0269 16.9645L12.0698 35.8297C11.4861 36.7507 10.0594 36.2054 10.2388 35.1299L12.3058 22.7354C12.4075 22.1258 11.9374 21.5709 11.3194 21.5709H1.81937C1.03076 21.5709 0.552462 20.7007 0.975137 20.0349L12.9487 1.17489C13.5333 0.254083 14.96 0.801103 14.7791 1.8767L12.6961 14.2633Z'
        fill='white'
      />
    </svg>
  ),
  defaultProps: {
    width: '25.6px',
    height: '25.6px',
    stroke: '#00BFB2',
    fill: 'transparent',
  },
})

export const UpdateIcon = createIcon({
  displayName: 'EditIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d='M11 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22H15C20 22 22 20 22 15V13'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path
        d='M16.0399 3.01976L8.15988 10.8998C7.85988 11.1998 7.55988 11.7898 7.49988 12.2198L7.06988 15.2298C6.90988 16.3198 7.67988 17.0798 8.76988 16.9298L11.7799 16.4998C12.1999 16.4398 12.7899 16.1398 13.0999 15.8398L20.9799 7.95976C22.3399 6.59976 22.9799 5.01976 20.9799 3.01976C18.9799 1.01976 17.3999 1.65976 16.0399 3.01976Z'
        strokeWidth='2'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
      <path
        d='M14.9099 4.1499C15.5799 6.5399 17.4499 8.4099 19.8499 9.0899'
        strokeWidth='2'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
    </>
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
    fill: 'transparent',
    stroke: '#00BFB2',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

// export const Update2Icon = createIcon({
//   displayName: 'aa',
//   viewBox: '0 0 18 18',
//   path: (
//     <>
//       <path
//         d='M9.94501 2.6965L3.78751 9.214C3.55501 9.4615 3.33001 9.949 3.28501 10.2865L3.00751 12.7165C2.91001 13.594 3.54001 14.194 4.41001 14.044L6.82501 13.6315C7.16251 13.5715 7.63501 13.324 7.86751 13.069L14.025 6.5515C15.09 5.4265 15.57 4.144 13.9125 2.5765C12.2625 1.024 11.01 1.5715 9.94501 2.6965Z'
//         strokeWidth='1.5'
//         strokeMiterlimit='10'
//         strokeLinecap='round'
//         strokeLinejoin='round'
//       />
//       <path
//         d='M8.91748 3.78906C9.23998 5.85906 10.92 7.44156 13.005 7.65156'
//         strokeWidth='1.5'
//         strokeMiterlimit='10'
//         strokeLinecap='round'
//         strokeLinejoin='round'
//       />
//       <path d='M2.25 16.5H15.75' strokeWidth='1.5' strokeMiterlimit='10' strokeLinecap='round' strokeLinejoin='round' />
//     </>
//   ),
//   defaultProps: {
//     width: '18px',
//     height: '18px',
//     stroke: 'white',
//     fill: 'transparent',
//   },
// })

export const TooltipIcon = createIcon({
  displayName: 'TooltipIcon',
  viewBox: '0 -3 20 20',
  path: (
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M14.3996 8.00156C14.3996 11.5362 11.5342 14.4016 7.99961 14.4016C4.46499 14.4016 1.59961 11.5362 1.59961 8.00156C1.59961 4.46694 4.46499 1.60156 7.99961 1.60156C11.5342 1.60156 14.3996 4.46694 14.3996 8.00156ZM8.79961 4.80156C8.79961 5.24339 8.44144 5.60156 7.99961 5.60156C7.55778 5.60156 7.19961 5.24339 7.19961 4.80156C7.19961 4.35973 7.55778 4.00156 7.99961 4.00156C8.44144 4.00156 8.79961 4.35973 8.79961 4.80156ZM7.19961 7.20156C6.75778 7.20156 6.39961 7.55973 6.39961 8.00156C6.39961 8.44339 6.75778 8.80156 7.19961 8.80156V11.2016C7.19961 11.6434 7.55778 12.0016 7.99961 12.0016H8.79961C9.24144 12.0016 9.59961 11.6434 9.59961 11.2016C9.59961 10.7597 9.24144 10.4016 8.79961 10.4016V8.00156C8.79961 7.55973 8.44144 7.20156 7.99961 7.20156H7.19961Z'
    />
  ),
  defaultProps: {
    width: '25px',
    height: '25px',
    stroke: 'typography.100',
    fill: 'transparent',
  },
})

export const ApplePayIcon = createIcon({
  displayName: 'ApplePayIcon',
  viewBox: '0 0 49 20',
  path: (
    <>
      <g clipPath='url(#clip0_29017_151834)'>
        <path
          d='M8.95611 2.5785C8.38189 3.25404 7.46314 3.78687 6.54439 3.71075C6.42954 2.79734 6.87935 1.82683 7.40571 1.2274C7.97993 0.532826 8.98482 0.038059 9.79829 0C9.894 0.951475 9.52075 1.88392 8.95611 2.5785ZM9.78872 3.89153C8.45845 3.81541 7.31958 4.6432 6.68794 4.6432C6.04673 4.6432 5.08013 3.92959 4.02739 3.94862C2.65884 3.96765 1.38599 4.73834 0.687355 5.96575C-0.748191 8.42055 0.314113 12.0552 1.70181 14.0533C2.3813 15.0428 3.19478 16.1275 4.26665 16.0894C5.28111 16.0514 5.68306 15.4329 6.90806 15.4329C8.14263 15.4329 8.49673 16.0894 9.56861 16.0704C10.6788 16.0514 11.3774 15.0809 12.0569 14.0913C12.8321 12.9686 13.1479 11.8744 13.167 11.8173C13.1479 11.7983 11.0233 10.9895 11.0042 8.55376C10.985 6.5176 12.679 5.5471 12.7555 5.49001C11.7985 4.08183 10.3055 3.92959 9.78872 3.89153ZM17.4737 1.13225V15.9657H19.7897V10.8944H22.9958C25.9243 10.8944 27.9819 8.89629 27.9819 6.00381C27.9819 3.11132 25.9626 1.13225 23.0723 1.13225H17.4737ZM19.7897 3.07326H22.4598C24.4696 3.07326 25.618 4.13892 25.618 6.01332C25.618 7.88773 24.4696 8.96289 22.4502 8.96289H19.7897V3.07326ZM32.212 16.0799C33.6667 16.0799 35.0161 15.3473 35.6286 14.1865H35.6764V15.9657H37.8202V8.5823C37.8202 6.44148 36.0975 5.06185 33.4465 5.06185C30.987 5.06185 29.1686 6.46051 29.1016 8.38249H31.1879C31.3602 7.46908 32.212 6.86965 33.3795 6.86965C34.7959 6.86965 35.5903 7.52617 35.5903 8.73454V9.55281L32.7001 9.72407C30.0108 9.88582 28.5561 10.98 28.5561 12.883C28.5561 14.8049 30.0586 16.0799 32.212 16.0799ZM32.834 14.3197C31.5995 14.3197 30.8147 13.7298 30.8147 12.8259C30.8147 11.8934 31.5708 11.3511 33.0159 11.2655L35.5903 11.1037V11.941C35.5903 13.3302 34.4036 14.3197 32.834 14.3197ZM40.6817 20C42.9403 20 44.0026 19.1437 44.9309 16.5461L48.9983 5.20457H46.644L43.9165 13.9677H43.8686L41.1411 5.20457H38.7198L42.6436 16.0038L42.4331 16.6603C42.079 17.7736 41.5047 18.2017 40.4807 18.2017C40.2989 18.2017 39.9448 18.1827 39.8012 18.1637V19.9429C39.9352 19.981 40.5094 20 40.6817 20Z'
          fill='black'
        />
      </g>
      <defs>
        <clipPath id='clip0_29017_151834'>
          <rect width='49' height='20' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '49px',
    height: '20px',
    fill: 'transparent',
  },
})

export const ErrorRedIcon = createIcon({
  displayName: 'ErrorRedIcon',
  viewBox: '0 0 24 24',
  path: (
    <path d='M12 0C5.376 0 0 5.376 0 12C0 18.624 5.376 24 12 24C18.624 24 24 18.624 24 12C24 5.376 18.624 0 12 0ZM11.004 6.612C11.004 6.06 11.448 5.616 12 5.616C12.552 5.616 12.996 6.06 12.996 6.612V13.908C12.996 14.46 12.552 14.904 12 14.904C11.448 14.904 11.004 14.46 11.004 13.908V6.612ZM12.024 18.384C11.472 18.384 11.016 17.94 11.016 17.388C11.016 16.836 11.46 16.392 12.012 16.392H12.024C12.576 16.392 13.02 16.836 13.02 17.388C13.02 17.94 12.576 18.384 12.024 18.384Z' />
  ),
  defaultProps: {
    width: '24px',
    height: '24px',
    fill: '#FF5630',
  },
})

export const ErrorArrowIcon = createIcon({
  displayName: 'ErrorRedIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M4.16797 9.9974H15.8346M15.8346 9.9974L10.0013 4.16406M15.8346 9.9974L10.0013 15.8307'
      stroke='#FF5630'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '20px',
    height: '20px',
    fill: '#FF5630',
    stroke: '#FF5630',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const ActiveIcon = createIcon({
  displayName: 'ActiveIcon',
  path: (
    <path
      d='M13.946 3.17636C13.7706 2.72969 13.2186 2.60845 12.8517 2.94026C12.7751 3.01045 12.6666 3.10617 12.539 3.22422C12.4433 3.08383 12.3444 2.94345 12.2391 2.80945C11.8754 2.35321 11.4702 1.95439 11.0299 1.61301C11.0044 1.58748 10.9757 1.56196 10.9438 1.53962C10.7683 1.412 10.5864 1.29076 10.4014 1.17909C10.3886 1.17271 10.3759 1.16314 10.3663 1.15676C10.3567 1.15038 10.3439 1.144 10.3344 1.13762C10.3312 1.13443 10.3248 1.13124 10.3216 1.13124C9.26236 0.509085 8.06591 0.183652 6.83756 0.190033C6.46746 0.190033 6.09736 0.221939 5.73364 0.282559C5.27421 0.359131 4.81796 0.480371 4.36491 0.649468C1.74868 1.63215 0.0002795 4.21647 0.0002795 6.97627C-0.00929206 8.0419 0.226806 9.11392 0.734099 10.0902C2.18898 12.9074 5.30931 14.2921 8.26692 13.6572C9.37403 13.4307 10.3759 12.9394 11.2692 12.2023C11.3171 12.1609 11.3617 12.1194 11.4 12.0747C11.9807 11.5579 12.4688 10.9453 12.8613 10.237C12.9921 10.0009 13.0144 9.76478 12.8772 9.52549C12.7496 9.29897 12.5422 9.18411 12.287 9.1873C11.9934 9.19049 11.7956 9.34683 11.6521 9.59888C10.8863 10.9676 9.74732 11.8386 8.16801 12.2821C7.46291 12.448 6.70356 12.4735 5.88041 12.3619C5.69536 12.3108 5.38588 12.2438 5.08916 12.1449C2.21131 11.1718 0.67986 8.08657 1.66573 5.22148C1.69764 5.12577 1.73592 5.03005 1.77421 4.93753C1.7774 4.93115 1.78059 4.92157 1.78378 4.91519C1.79335 4.89286 1.80292 4.87053 1.81249 4.84819C2.74094 2.70416 5.01578 1.28757 7.56181 1.60024C7.67986 1.6162 7.79472 1.63215 7.90958 1.65448C8.44559 1.76296 8.9784 1.9512 9.49208 2.22878C9.53355 2.25111 9.57184 2.27344 9.61332 2.29578C9.63565 2.30854 9.66117 2.3213 9.68351 2.33725C9.69627 2.34682 9.71222 2.35321 9.72498 2.36278C9.76646 2.3883 9.80794 2.41383 9.84622 2.43935C9.84941 2.44254 9.8558 2.44573 9.85899 2.44892C9.89727 2.47445 9.93875 2.49997 9.97704 2.52868C10.596 2.95621 11.1033 3.50179 11.5117 4.14628C10.4333 5.12258 9.08688 6.34455 8.64978 6.74017C8.12015 7.22513 7.58734 7.71009 7.05452 8.19824C7.00028 8.15357 6.94604 8.11209 6.8918 8.06743C6.57913 7.81538 6.26327 7.56332 5.9506 7.31127C5.46245 6.91884 4.9743 6.52641 4.48296 6.13716C4.21496 5.9234 3.83847 5.94892 3.60876 6.17226C3.3599 6.41474 3.31523 6.77846 3.51623 7.05603C3.57685 7.13899 3.663 7.20918 3.74276 7.27618C4.1001 7.56332 4.45744 7.85047 4.81477 8.13762C5.36673 8.5811 5.9155 9.02458 6.46746 9.46488C6.51851 9.50635 6.56956 9.55102 6.6238 9.5893C6.83118 9.78074 7.12152 9.82859 7.37995 9.69778C7.40229 9.68502 7.42781 9.67226 7.45014 9.65631C7.49481 9.62759 7.5331 9.59888 7.56819 9.56378C7.59053 9.54464 7.60967 9.52549 7.632 9.50635C8.89226 8.35457 12.3316 5.24382 13.5919 4.09204C13.6621 4.02823 13.7355 3.96442 13.8025 3.89423C14.0035 3.69003 14.0513 3.44436 13.946 3.17636Z'
      fill='#04C200'
    />
  ),
  defaultProps: {
    viewBox: '0 0 14 14',
    width: '16px',
    height: '16px',
    fill: 'none',
  },
})

export const TranslateIcon = createIcon({
  displayName: 'TranslateIcon',
  path: (
    <path
      d='M3.05493 11H5C6.10457 11 7 11.8954 7 13V14C7 15.1046 7.89543 16 9 16C10.1046 16 11 16.8954 11 18V20.9451M8 3.93552V5.5C8 6.88071 9.11929 8 10.5 8H11C12.1046 8 13 8.89543 13 10C13 11.1046 13.8954 12 15 12C16.1046 12 17 11.1046 17 10C17 8.89543 17.8954 8 19 8L20.0645 8M15 20.4879V18C15 16.8954 15.8954 16 17 16H20.0645M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z'
      stroke='#00BFB2'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    viewBox: '0 0 24 24',
    width: '24px',
    height: '24px',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CalendarIcon = createIcon({
  displayName: 'CalendarIcon',
  path: (
    <path
      d='M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z'
      stroke='#8A94A6'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '1.5rem',
    height: '1.5rem',
    viewBox: '0 0 24 24',
    fill: 'none',
  },
})

export const CompanyLogoIcon = createIcon({
  displayName: 'CompanyLogoIcon',
  viewBox: '0 0 62 62', // keep the original art-board
  path: (
    <>
      <rect width='62' height='62' rx='31' fill='url(#grad)' />
      <path
        d='M13.6292 14.0108L38.4802 12.6423C39.6423 12.5783 40.6344 13.4659 40.6984 14.6219L42.1432 40.7246C42.2072 41.8807 41.3191 42.8727 40.157 42.9367L15.3059 44.3052C14.1438 44.3692 13.1516 43.4807 13.0876 42.3246L11.6429 16.222C11.5789 15.0659 12.467 14.0748 13.6292 14.0108Z'
        fill='#E1E3FB'
        stroke='white'
      />
      <path
        d='M21.6501 18.3401L46.2932 14.8593C47.4454 14.6967 48.5092 15.4965 48.672 16.6428L52.3469 42.5277C52.5095 43.674 51.7102 44.737 50.5581 44.8999L25.915 48.3807C24.7627 48.5434 23.6982 47.7437 23.5352 46.5973L19.8604 20.7124C19.6977 19.5659 20.4978 18.5029 21.6501 18.3401Z'
        fill='#E1E3FB'
        stroke='white'
      />

      <path
        d='M17.9775 15.5H42.8662C44.03 15.5 44.9716 16.4408 44.9716 17.5986V43.7412C44.9716 44.8991 44.03 45.8398 42.8662 45.8398H17.9775C16.8136 45.8398 15.8721 44.8991 15.8721 43.7412V17.5986C15.8721 16.5132 16.6994 15.6184 17.7617 15.5107L17.9775 15.5Z'
        fill='white'
      />
      <path
        d='M17.9775 15.5H42.8662C44.03 15.5 44.9716 16.4408 44.9716 17.5986V43.7412C44.9716 44.8991 44.03 45.8398 42.8662 45.8398H17.9775C16.8136 45.8398 15.8721 44.8991 15.8721 43.7412V17.5986C15.8721 16.5132 16.6994 15.6184 17.7617 15.5107L17.9775 15.5Z'
        stroke='#EEF1FF'
        style={{ mixBlendMode: 'multiply' }}
      />

      {/* avatar circle (clipped) */}
      <g clipPath='url(#clip0)'>
        <path
          d='M34.8959 28.7859C36.8383 28.7859 38.413 27.229 38.413 25.3085C38.413 23.388 36.8383 21.8311 34.8959 21.8311C32.9535 21.8311 31.3789 23.388 31.3789 25.3085C31.3789 27.229 32.9535 28.7859 34.8959 28.7859Z'
          fill='#BCBFED'
        />
      </g>

      {/* rosette ribbons */}
      <path
        d='M43.8953 43.0635C44.2853 43.7297 43.8054 44.5679 43.0334 44.5687L31.9323 44.5811C31.1603 44.582 30.6785 43.7449 31.067 43.0778L34.0787 37.9065L36.0269 34.5614C36.6678 33.4615 38.2725 33.4597 38.9155 34.5581L40.8847 37.9216L43.8953 43.0635Z'
        fill='#DADCFA'
      />
      <path
        d='M36.1925 43.0718C36.5825 43.738 36.1026 44.5762 35.3306 44.577L18.3014 44.596C17.5295 44.5969 17.0477 43.7598 17.4362 43.0927L22.0261 35.2118L23.3246 32.9822L25.3546 29.4969C25.9952 28.397 27.5999 28.3952 28.2432 29.4936L30.9278 34.0791L31.8791 35.7042L36.1925 43.0718Z'
        fill='#BCBFED'
      />

      {/* definitions */}
      <defs>
        <linearGradient id='grad' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>

        <clipPath id='clip0'>
          <rect width='28.2157' height='22.2305' transform='translate(13.9941 21.8311)' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
  },
})

export const CompanyCaptableIcon = createIcon({
  displayName: 'CompanyCaptableIcon',
  viewBox: '0 0 62 62',
  path: (
    <>
      <rect width='62' height='62' rx='31' fill='url(#paint0_linear)' />

      <path
        d='M17.5391 18.4468H46.1094C47.5672 18.4468 48.749 19.6286 48.749 21.0864V41.3491C48.749 42.8069 47.5672 43.9888 46.1094 43.9888H17.5391C16.0813 43.9888 14.8995 42.8069 14.8994 41.3491V21.0864C14.8994 19.7196 15.9383 18.5955 17.2695 18.4604L17.5391 18.4468Z'
        fill='#E1E3FB'
        stroke='white'
      />

      <path
        d='M13.6123 14.7432H42.1826C43.6405 14.7432 44.8223 15.925 44.8223 17.3828V37.6455C44.8222 39.1033 43.6404 40.2852 42.1826 40.2852H13.6123C12.1545 40.2851 10.9727 39.1033 10.9727 37.6455V17.3828C10.9727 15.925 12.1545 14.7432 13.6123 14.7432Z'
        fill='white'
      />
      <path
        d='M13.6123 14.7432H42.1826C43.6405 14.7432 44.8223 15.925 44.8223 17.3828V37.6455C44.8222 39.1033 43.6404 40.2852 42.1826 40.2852H13.6123C12.1545 40.2851 10.9727 39.1033 10.9727 37.6455V17.3828C10.9727 15.925 12.1545 14.7432 13.6123 14.7432Z'
        stroke='#EEF1FF'
        style={{ mixBlendMode: 'multiply' }}
      />

      {/* Side panel */}
      <path
        d='M37.6934 37.7559H51.4102C51.9965 37.7559 52.4716 38.2311 52.4717 38.8174V45.8789C52.4717 46.4653 51.9965 46.9404 51.4102 46.9404H37.6934C37.1071 46.9403 36.6318 46.4652 36.6318 45.8789V38.8174L36.6377 38.709C36.6921 38.1737 37.1437 37.756 37.6934 37.7559Z'
        fill='white'
        stroke='white'
        style={{ mixBlendMode: 'multiply' }}
      />

      {/* Dots & bars on right */}
      <circle cx='39.094' cy='40.3738' r='1.1311' fill='#BCBFED' />
      <rect x='42.4979' y='39.6953' width='5.1491' height='1.1442' rx='0.5721' fill='#BCBFED' />
      <rect x='49.1563' y='39.5664' width='1.9826' height='1.4016' rx='0.7' fill='#BCBFED' />
      <circle cx='39.094' cy='44.3235' r='1.1311' fill='#BCBFED' />
      <rect x='42.4979' y='43.6448' width='5.1491' height='1.1442' rx='0.5721' fill='#BCBFED' />
      <rect x='49.1572' y='43.5161' width='1.9826' height='1.4016' rx='0.7' fill='#BCBFED' />

      {/* Vertical bars */}
      <path d='M15.8392 19.3534V34.9413' stroke='url(#paint1_linear)' strokeLinecap='round' />
      <path d='M38.5473 20.0469V34.941' stroke='url(#paint2_linear)' strokeLinecap='round' />
      <path d='M24.9219 24.3858V34.9409' stroke='url(#paint3_linear)' strokeLinecap='round' />
      <path d='M34.0052 23.1193V34.9412' stroke='url(#paint4_linear)' strokeLinecap='round' />
      <path d='M20.3804 19.7016V34.9414' stroke='url(#paint5_linear)' strokeLinecap='round' />
      <path d='M29.4636 19.9458V34.9412' stroke='url(#paint6_linear)' strokeLinecap='round' />

      {/* Zig-zag line */}
      <path
        d='M16.0166 26.2144L20.5581 29.5108L25.0995 27.8626L29.641 29.5108L34.1825 31.3665L38.724 26.2144'
        stroke='#6490EB'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />

      {/* Gradients */}
      <defs>
        <linearGradient id='paint0_linear' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint1_linear'
          x1='13.8046'
          y1='19.8757'
          x2='17.8739'
          y2='35.0629'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint2_linear'
          x1='36.5985'
          y1='20.5462'
          x2='40.4945'
          y2='35.0864'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint3_linear'
          x1='23.5166'
          y1='24.7395'
          x2='26.3278'
          y2='35.231'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint4_linear'
          x1='32.4414'
          y1='23.5155'
          x2='35.5693'
          y2='35.189'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint5_linear'
          x1='18.3897'
          y1='20.2122'
          x2='22.3722'
          y2='35.0748'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient
          id='paint6_linear'
          x1='27.5031'
          y1='20.4483'
          x2='31.4243'
          y2='35.0827'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CompanyStampIcon = createIcon({
  displayName: 'CompanyStampIcon',
  viewBox: '0 0 62 62',
  path: (
    <>
      {/* background circle */}
      <circle cx='31' cy='31' r='31' fill='url(#grad)' />

      {/* phone body, masked to the circle */}
      <mask id='mask0' maskUnits='userSpaceOnUse' x='0' y='0' width='62' height='62'>
        <circle cx='31' cy='31' r='30.5' fill='white' />
        <circle cx='31' cy='31' r='30.5' stroke='#EEF1FF' />
      </mask>

      <g mask='url(#mask0)'>
        <rect x='10.6123' y='13.5' width='39.7759' height='54' rx='3.75793' fill='white' />
        <rect
          x='10.6123'
          y='13.5'
          width='39.7759'
          height='54'
          rx='3.75793'
          stroke='#EEF1FF'
          style={{ mixBlendMode: 'multiply' }}
        />

        {/* UI lines inside the phone */}
        <rect x='15' y='18' width='15' height='2' rx='1' fill='#DBDDFA' />
        <rect x='15' y='22' width='8' height='2' rx='1' fill='#DBDDFA' />
        <rect x='15' y='32' width='32' height='2' rx='1' fill='#DBDDFA' />
        <rect x='15' y='37' width='32' height='2' rx='1' fill='#DBDDFA' />
        <rect x='15' y='42' width='32' height='2' rx='1' fill='#DBDDFA' />
        <rect x='15' y='53' width='10' height='2' rx='1' fill='#DBDDFA' />
      </g>

      {/* decorative graph & legend (clipped) */}
      <g clipPath='url(#clip0)'>
        {/* big squiggly line / chart */}
        <path d='M33.366 46.9716C32.3804 46.8565 31.385 46.8336 30.3952...' fill='#6490EB' />
        {/* footer “legend” glyphs */}
        <path d='M16.4025 51.8986...' fill='#6490EB' />
        <path d='M18.1254 51.8974...' fill='#6490EB' />
        <path d='M21.1929 51.9739...' fill='#6490EB' />
        <path d='M23.0539 50.5134...' fill='#6490EB' />
        <path d='M24.7001 51.6464...' fill='#6490EB' />
        <path d='M26.9176 50.4458...' fill='#6490EB' />
        <path d='M28.17 51.4325...' fill='#6490EB' />
        <path d='M31.2409 50.4458...' fill='#6490EB' />
        <path d='M32.7636 51.6464...' fill='#6490EB' />
        <path d='M34.9713 51.9737...' fill='#6490EB' />
        <path d='M37.0005 51.69...' fill='#6490EB' />
      </g>

      {/* defs */}
      <defs>
        <linearGradient id='grad' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>

        <clipPath id='clip0'>
          <rect width='21' height='17' transform='translate(16 35)' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CompanyBoardIcon = createIcon({
  displayName: 'CompanyBoardIcon',
  viewBox: '0 0 62 62',
  path: (
    <>
      <circle cx='31' cy='31' r='31' fill='url(#grad)' />

      {/* First white card */}
      <rect x='7.248' y='18.2607' width='48.3622' height='13.5314' rx='2.00888' fill='white' />
      <rect
        x='7.3736'
        y='18.3863'
        width='48.1111'
        height='13.2803'
        rx='1.88333'
        stroke='#DADDE4'
        strokeOpacity='0.5'
        strokeWidth='0.251111'
        style={{ mixBlendMode: 'multiply' }}
      />
      {/* Avatar and face */}
      <rect x='10.2' y='20.4759' width='8.8704' height='8.8704' rx='4.4352' fill='#DBDDFA' />
      <rect x='10.2' y='20.4759' width='8.8704' height='8.8704' rx='4.4352' stroke='white' strokeWidth='0.320617' />
      <path
        opacity='0.4'
        d='M14.6354 29.1842C16.9954 29.1842 18.9085 27.271 18.9085 24.911C18.9085 22.5511 16.9954 20.6379 14.6354 20.6379C12.2754 20.6379 10.3623 22.5511 10.3623 24.911C10.3623 27.271 12.2754 29.1842 14.6354 29.1842Z'
        fill='#DBDDFA'
      />
      <path
        d='M14.6323 22.7441C13.748 22.7441 13.0303 23.4618 13.0303 24.3461C13.0303 25.2133 13.7095 25.9182 14.6109 25.9438C15.5507 25.9139 16.23 25.2133 16.2342 24.3461C16.2342 23.4618 15.5166 22.7441 14.6323 22.7441Z'
        fill='white'
      />
      <path
        d='M17.5316 28.0519C16.771 28.7527 15.754 29.1843 14.6345 29.1843C13.5149 29.1843 12.4979 28.7527 11.7373 28.0519C11.8399 27.6631 12.1176 27.3084 12.5236 27.0349C13.6901 26.2572 15.5874 26.2572 16.7454 27.0349C17.1556 27.3084 17.4291 27.6631 17.5316 28.0519Z'
        fill='white'
      />

      {/* First row card with icon and input */}
      <rect x='21.05' y='21.9409' width='14.6167' height='6.03995' rx='1.22953' fill='#F2F4FE' />
      <g clipPath='url(#clip0)'>
        <path
          d='M22.7422 25.6844L24.191 24.2356M22.7422 24.2356L24.191 25.6844'
          stroke='#6490EB'
          strokeWidth='0.614446'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <rect x='26.2441' y='23.6328' width='7.97273' height='2.65758' rx='0.614766' fill='#DADCFA' />

      {/* First row, right-side box */}
      <rect x='37.7217' y='21.9409' width='14.6167' height='6.03995' rx='1.22953' fill='#F2F4FE' />
      <g clipPath='url(#clip1)'>
        <path
          d='M39.6543 25.0809L40.1372 25.5638L41.3446 24.3564'
          stroke='#6490EB'
          strokeWidth='0.614446'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <rect x='42.916' y='23.6328' width='7.97273' height='2.65758' rx='0.614766' fill='#DADCFA' />

      {/* Second white card */}
      <rect x='2.55566' y='27.2832' width='56.9041' height='15.9214' rx='2.3637' fill='white' />
      <rect
        x='2.7034'
        y='27.4309'
        width='56.6087'
        height='15.6259'
        rx='2.21597'
        stroke='#DADDE4'
        strokeOpacity='0.5'
        strokeWidth='0.295462'
        style={{ mixBlendMode: 'multiply' }}
      />
      {/* Avatar in second card */}
      <rect x='6.03013' y='29.8895' width='10.4371' height='10.4371' rx='5.21856' fill='#DBDDFA' />
      <rect
        x='6.03013'
        y='29.8895'
        width='10.4371'
        height='10.4371'
        rx='5.21856'
        stroke='white'
        strokeWidth='0.377245'
      />
      <path
        opacity='0.4'
        d='M11.2485 40.136C14.0253 40.136 16.2764 37.885 16.2764 35.1082C16.2764 32.3314 14.0253 30.0803 11.2485 30.0803C8.47174 30.0803 6.2207 32.3314 6.2207 35.1082C6.2207 37.885 8.47174 40.136 11.2485 40.136Z'
        fill='#DBDDFA'
      />
      <path
        d='M11.2468 32.5588C10.2065 32.5588 9.3623 33.4031 9.3623 34.4433C9.3623 35.4634 10.1613 36.2926 11.2216 36.3227C12.3272 36.2875 13.1262 35.4634 13.1312 34.4433C13.1312 33.4031 12.287 32.5588 11.2468 32.5588Z'
        fill='white'
      />
      <path
        d='M14.6556 38.8036C13.7607 39.6281 12.5641 40.136 11.2468 40.136C9.92947 40.136 8.73285 39.6281 7.83789 38.8036C7.95856 38.346 8.28537 37.9287 8.76301 37.607C10.1356 36.6919 12.368 36.6919 13.7305 37.607C14.2132 37.9287 14.535 38.346 14.6556 38.8036Z'
        fill='white'
      />

      {/* Second row buttons */}
      <rect x='18.7969' y='31.6143' width='17.1983' height='7.10674' rx='1.4467' fill='#F2F4FE' />
      <g clipPath='url(#clip2)'>
        <path
          d='M20.7861 36.0187L22.4904 34.3145M20.7861 34.3145L22.4904 36.0187'
          stroke='#6490EB'
          strokeWidth='0.722778'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <rect x='24.9092' y='33.604' width='9.38089' height='3.12697' rx='0.723348' fill='#DADCFA' />

      {/* Second row, right button group */}
      <rect x='38.4121' y='31.614' width='17.1983' height='7.10674' rx='1.4467' fill='#F2F4FE' />
      <g clipPath='url(#clip3)'>
        <path
          d='M40.6846 35.3084L41.2527 35.8765L42.6729 34.4563'
          stroke='#6490EB'
          strokeWidth='0.722778'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <rect x='44.5234' y='33.604' width='9.38089' height='3.12697' rx='0.723348' fill='#DADCFA' />

      {/* Definitions */}
      <defs>
        <linearGradient id='grad' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <clipPath id='clip0'>
          <rect width='2.89767' height='2.89767' transform='translate(22.0176 23.5112)' fill='white' />
        </clipPath>
        <clipPath id='clip1'>
          <rect width='2.89767' height='2.89767' transform='translate(39.0508 23.5112)' fill='white' />
        </clipPath>
        <clipPath id='clip2'>
          <rect width='3.40855' height='3.40855' transform='translate(19.9336 33.4624)' fill='white' />
        </clipPath>
        <clipPath id='clip3'>
          <rect width='3.40855' height='3.40855' transform='translate(39.9746 33.4622)' fill='white' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
    _rtl: { transform: 'scaleX(-1)' },
  },
})

export const CompanyCommitteeIcon = createIcon({
  displayName: 'CompanyCommitteeIcon',
  viewBox: '0 0 62 62',
  path: (
    <>
      <circle cx='31' cy='31' r='31' fill='url(#bankGradient)' />
      <path
        d='M17.7838 9.13188L9.61328 24.9122L20.8278 32.3619H42.0551L52.0681 24.9923C49.4247 19.9191 44.0577 9.6125 43.7373 8.97168C43.4169 8.33085 42.4824 8.22404 42.0551 8.25074H19.2257C18.3285 8.25074 17.8906 8.83817 17.7838 9.13188Z'
        fill='#BCBFED'
        stroke='#EEF1FF'
      />
      <rect x='24.5312' y='7.86987' width='12.7778' height='6.85013' rx='3.42506' fill='#C8CBF6' stroke='#EEF1FF' />
      <rect x='20.2051' y='11.8752' width='21.5891' height='6.85013' rx='3.42506' fill='#D2D4F8' stroke='#EDEFF9' />
      <rect x='17.8037' y='16.5212' width='26.2351' height='6.85013' rx='3.42506' fill='#E1E3FB' stroke='#EDEFF9' />
      <rect x='15.0791' y='21.0068' width='31.522' height='12.938' rx='6.46899' fill='#F3F5FE' stroke='#EEF1FF' />
      <rect x='24.9922' y='24.0312' width='11.8553' height='1.28165' rx='0.640827' fill='white' />
      <rect x='17.623' y='26.4346' width='26.7545' height='1.28165' rx='0.640827' fill='white' />
      <rect x='17.623' y='28.998' width='26.7545' height='1.28165' rx='0.640827' fill='white' />
      <path
        d='M20.1869 25.0723H9.61328V44.4573C9.61328 47.6614 12.6038 48.6227 14.0991 48.7028H48.2231C51.1709 47.8697 52.0681 45.5253 52.1482 44.4573V25.0723H41.174C39.0112 25.0723 38.6908 27.2351 38.6107 28.9973C38.5466 30.4071 37.1421 30.7596 36.4479 30.7596H25.1533C23.7916 30.7596 23.4712 29.7183 23.311 28.9973C23.2469 25.9854 21.2016 25.1257 20.1869 25.0723Z'
        fill='#DEE0FB'
        stroke='white'
      />
      <rect x='12.9766' y='41.4934' width='11.8553' height='1.44186' rx='0.72093' fill='#F3F5FE' />
      <rect x='12.9766' y='43.8965' width='9.4522' height='1.44186' rx='0.72093' fill='#F3F5FE' />
      <defs>
        <linearGradient id='bankGradient' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
  },
})

export const CompanyPlanIcon = createIcon({
  displayName: 'CompanyPlanIcon',
  viewBox: '0 0 62 62',
  path: (
    <>
      <circle cx='31' cy='31' r='31' fill='url(#analyticsGradient)' />
      <mask id='analyticsMask' maskUnits='userSpaceOnUse' x='0' y='0' width='62' height='62'>
        <circle cx='31' cy='31' r='31' fill='url(#analyticsMaskGradient)' />
      </mask>
      <g mask='url(#analyticsMask)'>
        <rect x='10' y='16' width='43' height='58' rx='4' fill='#F2F4FE' stroke='white' />
        <g clipPath='url(#clip0)'>
          <path
            d='M46.178 32.0923C45.1491 29.1087 43.2112 26.5529 40.6979 24.7597C38.2625 23.0217 35.2866 22 32.0737 22C29.0304 22 26.2002 22.9163 23.8398 24.4897C23.6245 24.6335 23.5716 24.9289 23.7236 25.1392L32.0737 36.7027L45.9139 41.2271C46.1608 41.3081 46.4259 41.1642 46.4928 40.9119C46.8234 39.67 46.9998 38.3643 46.9998 37.0174C46.9998 35.292 46.7103 33.6345 46.178 32.0914V32.0923Z'
            fill='#BCBFED'
          />
          <path
            d='M32.4199 38.6948V52.3035C32.4199 52.5613 32.6316 52.7689 32.8876 52.7603C35.915 52.6626 38.705 51.6187 40.975 49.9133C43.2756 48.1847 45.0422 45.7764 45.9806 42.9837C46.0616 42.7431 45.93 42.4826 45.6903 42.4039L33.0146 38.2598C32.7211 38.1639 32.4204 38.3837 32.4204 38.6944L32.4199 38.6948Z'
            fill='#6490EB'
          />
          <path
            d='M22.7017 25.529C22.5524 25.3223 22.2639 25.2785 22.0612 25.4322C19.6765 27.2454 17.838 29.7473 16.839 32.6445C16.2953 34.2197 16 35.9121 16 37.6737C16 39.2204 16.2279 40.7133 16.6513 42.1212C17.6051 45.293 19.553 48.0305 22.13 49.9671C24.5546 51.7888 27.5364 52.9018 30.7714 52.9995C31.0274 53.0072 31.239 52.8001 31.239 52.5428V37.5004C31.239 37.4041 31.2087 37.31 31.1522 37.2318L22.7017 25.529Z'
            fill='#DEE0FB'
          />
        </g>
      </g>
      <defs>
        <linearGradient id='analyticsGradient' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F9FBFF' />
        </linearGradient>
        <linearGradient id='analyticsMaskGradient' x1='31' y1='0' x2='31' y2='62' gradientUnits='userSpaceOnUse'>
          <stop stopColor='#D9DBFA' />
          <stop offset='1' stopColor='#F8FBFF' stopOpacity='0.98' />
        </linearGradient>
        <clipPath id='clip0'>
          <rect width='31' height='31' fill='white' transform='translate(16 22)' />
        </clipPath>
      </defs>
    </>
  ),
  defaultProps: {
    width: '55px',
    height: '55px',
    fill: 'none',
  },
})

export const CheckCircleIcon = createIcon({
  displayName: 'CheckCircleIcon',
  viewBox: '0 0 30 30',
  path: (
    <>
      {/* green background circle */}
      <circle cx='15' cy='15' r='15' fill='#00BFB2' />

      <path d='M8 15L12 19L22 9' stroke='white' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
    </>
  ),

  defaultProps: {
    width: '30px',
    height: '30px',
    fill: 'none',
  },
})

export const CalendarEditIcon = createIcon({
  displayName: 'CalendarEditIcon',
  viewBox: '0 0 21 22',
  path: (
    <>
      <path d='M7.00781 2.60645V5.1245' />
      <path d='M13.7227 2.60645V5.1245' />
      <path d='M3.23047 8.55762H17.4994' />
      <path d='M16.4176 14.1641L13.4463 17.1354C13.3288 17.2529 13.2197 17.4711 13.1945 17.6306L13.035 18.7637C12.9763 19.175 13.2616 19.4604 13.6729 19.4016L14.8061 19.2421C14.9655 19.217 15.1921 19.1078 15.3013 18.9903L18.2726 16.019C18.7846 15.507 19.028 14.9111 18.2726 14.1557C17.5256 13.4087 16.9296 13.6521 16.4176 14.1641Z' />
      <path d='M15.9883 14.5923C16.2401 15.4988 16.9451 16.2038 17.8516 16.4556' />
      <path d='M10.3667 19.3932H7.00925C4.07153 19.3932 2.8125 17.7145 2.8125 15.1965V8.06199C2.8125 5.54393 4.07153 3.86523 7.00925 3.86523H13.7241C16.6618 3.86523 17.9208 5.54393 17.9208 8.06199V10.9997' />
      <path d='M10.3604 12.427H10.3679' strokeWidth='2' />
      <path d='M7.25491 12.427H7.26245' strokeWidth='2' />
      <path d='M7.25491 14.9451H7.26245' strokeWidth='2' />
    </>
  ),
  defaultProps: {
    width: '21px',
    height: '22px',
    fill: 'none',
    stroke: '#00263A',
    strokeWidth: '1.5',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
  },
})

export const DocumentArrowIcon = createIcon({
  displayName: 'DocumentArrowIcon',
  viewBox: '0 0 21 22',
  path: (
    <>
      <path d='M8.10156 10.1606V15.1967L9.78026 13.518' />
      <path d='M8.10058 15.1968L6.42188 13.5181' />
      <path d='M19.0136 9.32125V13.518C19.0136 17.7148 17.3349 19.3935 13.1381 19.3935H8.10201C3.90526 19.3935 2.22656 17.7148 2.22656 13.518V8.4819C2.22656 4.28515 3.90526 2.60645 8.10201 2.60645H12.2988' />
      <path d='M19.0117 9.32125H15.6543C13.1362 9.32125 12.2969 8.4819 12.2969 5.96385V2.60645L19.0117 9.32125Z' />
    </>
  ),
  defaultProps: {
    width: '21px',
    height: '22px',
    fill: 'none',
    stroke: '#00263A',
    strokeWidth: '1.5',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
  },
})

export const FilterTableIcon = createIcon({
  displayName: 'FilterTableIcon',
  viewBox: '0 0 21 22',
  path: (
    <>
      <path d='M12.5547 4.70459H18.4301' />
      <path d='M12.5547 8.90137H18.4301' />
      <path d='M3.32422 13.0981H18.4325' />
      <path d='M3.32422 17.2949H18.4325' />
      <path d='M8.77829 8.00348V5.60294C8.77829 4.66286 8.40058 4.28516 7.45212 4.28516H5.05997C4.1199 4.28516 3.74219 4.66286 3.74219 5.60294V7.99508C3.74219 8.94355 4.1199 9.32126 5.05997 9.32126H7.45212C8.40058 9.32126 8.77829 8.94355 8.77829 8.00348Z' />
    </>
  ),
  defaultProps: {
    width: '21px',
    height: '22px',
    fill: 'none',
    stroke: '#00263A',
    strokeWidth: '1.5',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
  },
})

export const VideoCardIcon = createIcon({
  displayName: 'VideoCardIcon',
  viewBox: '0 0 22 22',
  path: (
    <>
      <path d='M11.436 18.0673H6.13132C3.47897 18.0673 2.59766 16.3046 2.59766 14.5336V7.46628C2.59766 4.81393 3.47897 3.93262 6.13132 3.93262H11.436C14.0884 3.93262 14.9697 4.81393 14.9697 7.46628V14.5336C14.9697 17.186 14.08 18.0673 11.436 18.0673Z' />
      <path d='M17.3021 15.2806L14.9688 13.6439V8.34756L17.3021 6.71083C18.4437 5.91345 19.3837 6.40027 19.3837 7.80199V14.1978C19.3837 15.5996 18.4437 16.0864 17.3021 15.2806Z' />
      <path d='M10.5715 10.1606C11.2669 10.1606 11.8306 9.59694 11.8306 8.9016C11.8306 8.20626 11.2669 7.64258 10.5715 7.64258C9.87618 7.64258 9.3125 8.20626 9.3125 8.9016C9.3125 9.59694 9.87618 10.1606 10.5715 10.1606Z' />
    </>
  ),
  defaultProps: {
    width: '22px',
    height: '22px',
    fill: 'none',
    stroke: '#00263A',
    strokeWidth: '1.5',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
  },
})

export const EbanaLogoTextIconEn = createIcon({
  displayName: 'EbanaLogoTextIconEn',
  viewBox: '0 0 117 16',
  path: (
    <>
      <path
        d='M89.1637 12.1608H87.9665C87.4781 12.1608 87.0172 11.9227 86.7367 11.5241L80.6553 1.91024C79.8112 0.714459 78.4337 0 76.9684 0H72.207V15.3446H75.5859V3.37927H76.9684C77.3366 3.37927 77.6848 3.55976 77.8952 3.86058L83.9765 13.4744C84.8907 14.768 86.381 15.5401 87.964 15.5401H92.5375V0.0225619H89.1587V12.1608H89.1637Z'
        fill='#00263A'
      />
      <path
        d='M0 4.34388V11.0247C0 13.3661 1.90356 15.2688 4.24042 15.2688H19.9598V11.8871H4.24042C3.76453 11.8871 3.37631 11.4985 3.37631 11.0222V9.37266H16.1201V5.99089H3.37631V4.34137C3.37631 3.86506 3.76453 3.4765 4.24042 3.4765H19.9598V0.0947266H4.24042C1.90105 0.0947266 0 1.99995 0 4.33886V4.34388Z'
        fill='#00263A'
      />
      <path
        d='M40.5163 0.0976562H24.7969V3.47943H40.5163C40.9921 3.47943 41.3804 3.86799 41.3804 4.3443V11.0251C41.3804 11.5014 40.9921 11.89 40.5163 11.89H24.7969V15.2718H40.5163C42.8556 15.2718 44.7567 13.3665 44.7567 11.0276V4.34681C44.7567 2.00539 42.8531 0.10267 40.5163 0.10267V0.0976562Z'
        fill='#00263A'
      />
      <path d='M35.5996 5.99414H24.7969V9.37591H35.5996V5.99414Z' fill='#00263A' />
      <path
        d='M64.1233 0.00488281H52.9475C50.6081 0.00488281 48.707 1.91011 48.707 4.24902V15.4272H52.0859V4.24902C52.0859 3.77271 52.4741 3.38415 52.95 3.38415H64.1258C64.6017 3.38415 64.99 3.77271 64.99 4.24902V15.4272H68.3688V4.24902C68.3688 1.9076 66.4652 0.00488281 64.1284 0.00488281H64.1233Z'
        fill='#00263A'
      />
      <path d='M62.4211 7.73828H54.6641V11.1201H62.4211V7.73828Z' fill='#00263A' />
      <path
        d='M111.909 0.00488281H100.733C98.3932 0.00488281 96.4922 1.91011 96.4922 4.24902V15.4272H99.871V4.24902C99.871 3.77271 100.259 3.38415 100.735 3.38415H111.911C112.387 3.38415 112.775 3.77271 112.775 4.24902V15.4272H116.154V4.24902C116.154 1.9076 114.25 0.00488281 111.913 0.00488281H111.909Z'
        fill='#00263A'
      />
      <path d='M110.093 7.73828H102.336V11.1201H110.093V7.73828Z' fill='#00263A' />
    </>
  ),
  defaultProps: {
    width: '117px',
    height: '16px',
  },
})

export const EbanaLogoTextIconAr = createIcon({
  displayName: 'EbanaLogoTextIconAr',
  viewBox: '0 0 149 50',
  path: (
    <>
      <path
        d='M120.788 43.3726H113.145C112.906 43.3726 112.747 43.5304 112.747 43.767V49.6055C112.747 49.8422 112.906 50 113.145 50H120.788C121.027 50 121.187 49.8422 121.187 49.6055V43.767C121.187 43.6093 121.027 43.3726 120.788 43.3726Z'
        fill='#00263A'
      />
      <path
        d='M148.577 40.7689H138.385C138.146 40.7689 137.987 40.9267 137.987 41.1634V44.0037C137.987 44.2404 138.146 44.3982 138.385 44.3982H148.577C148.816 44.3982 148.975 44.2404 148.975 44.0037V41.1634C149.054 40.9267 148.816 40.7689 148.577 40.7689Z'
        fill='#00263A'
      />
      <path
        d='M12.5009 1.47778H4.77751C4.53864 1.47778 4.37939 1.63558 4.37939 1.87227V7.71072C4.37939 7.94741 4.53864 8.10521 4.77751 8.10521H12.4213C12.6602 8.10521 12.8194 7.94741 12.8194 7.71072V1.87227C12.8991 1.63558 12.7398 1.47778 12.5009 1.47778Z'
        fill='#00263A'
      />
      <path
        d='M24.3642 1.47778H16.7204C16.4815 1.47778 16.3223 1.63558 16.3223 1.87227V7.71072C16.3223 7.94741 16.4815 8.10521 16.7204 8.10521H24.3642C24.6031 8.10521 24.7623 7.94741 24.7623 7.71072V1.87227C24.7623 1.63558 24.6031 1.47778 24.3642 1.47778Z'
        fill='#00263A'
      />
      <path
        d='M55.4174 2.97681H47.7736C47.5347 2.97681 47.3755 3.1346 47.3755 3.3713V9.20974C47.3755 9.44644 47.5347 9.60423 47.7736 9.60423H55.4174C55.6563 9.60423 55.8155 9.44644 55.8155 9.20974V3.3713C55.8951 3.1346 55.6563 2.97681 55.4174 2.97681Z'
        fill='#00263A'
      />
      <path
        d='M125.326 14.0224V30.7488H74.3676V0.609741H67.5996V30.3543C67.5996 34.2992 70.8641 37.4551 74.8453 37.4551H125.008C128.989 37.4551 132.253 34.2992 132.253 30.3543V14.0224H125.326Z'
        fill='#00263A'
      />
      <path
        d='M55.8953 14.0226V30.2756H54.9398C54.4621 30.2756 54.064 30.0389 53.7455 29.6444L44.7481 17.0207C43.3945 15.1272 41.1651 14.0226 38.856 14.0226H30.5752V30.7489H6.76795V20.7289H23.7276V14.0226H7.24569C3.26454 14.0226 0 17.1785 0 21.1234V30.3545C0 34.2994 3.26454 37.4553 7.24569 37.4553H37.4228V20.7289H38.3783C38.856 20.7289 39.2541 20.9656 39.5726 21.3601L48.8885 34.4571C50.2421 36.3507 52.4715 37.4553 54.7806 37.4553H62.7429V14.0226H55.8953Z'
        fill='#00263A'
      />
      <path
        d='M144.754 32.9581V5.42269C144.754 5.34379 144.675 5.26489 144.595 5.26489H138.066C137.986 5.26489 137.907 5.34379 137.907 5.42269V36.2718C137.907 36.903 138.464 37.4552 139.101 37.4552H148.656V32.9581H144.754Z'
        fill='#00263A'
      />
    </>
  ),
  defaultProps: {
    width: '149px',
    height: '50px',
  },
})

export const MenuSquareIcon = createIcon({
  displayName: 'MenuSquareIcon',
  viewBox: '0 0 44 44',
  path: (
    <>
      <rect x='0.5' y='0.5' width='43' height='43' rx='13.5' stroke='#00BFB2' />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M15.6992 17.5001C15.6992 17.003 16.1022 16.6001 16.5992 16.6001H27.3992C27.8963 16.6001 28.2992 17.003 28.2992 17.5001C28.2992 17.9972 27.8963 18.4001 27.3992 18.4001H16.5992C16.1022 18.4001 15.6992 17.9972 15.6992 17.5001Z'
        fill='#00BFB2'
      />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M15.6992 22.0001C15.6992 21.503 16.1022 21.1001 16.5992 21.1001H21.9992C22.4963 21.1001 22.8992 21.503 22.8992 22.0001C22.8992 22.4972 22.4963 22.9001 21.9992 22.9001H16.5992C16.1022 22.9001 15.6992 22.4972 15.6992 22.0001Z'
        fill='#00BFB2'
      />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M15.6992 26.5001C15.6992 26.003 16.1022 25.6001 16.5992 25.6001H27.3992C27.8963 25.6001 28.2992 26.003 28.2992 26.5001C28.2992 26.9972 27.8963 27.4001 27.3992 27.4001H16.5992C16.1022 27.4001 15.6992 26.9972 15.6992 26.5001Z'
        fill='#00BFB2'
      />
    </>
  ),
  defaultProps: {
    width: '44px',
    height: '44px',
    fill: 'none',
  },
})

export const MenuIcon = createIcon({
  displayName: 'MenuIcon',
  viewBox: '0 0 14 12',
  path: (
    <>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.699997 1.49998C0.699997 1.00292 1.10294 0.599976 1.6 0.599976H12.4C12.8971 0.599976 13.3 1.00292 13.3 1.49998C13.3 1.99703 12.8971 2.39998 12.4 2.39998H1.6C1.10294 2.39998 0.699997 1.99703 0.699997 1.49998Z'
        fill='#4E5D78'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.699997 5.99998C0.699997 5.50292 1.10294 5.09998 1.6 5.09998H7C7.49705 5.09998 7.9 5.50292 7.9 5.99998C7.9 6.49703 7.49705 6.89997 7 6.89997H1.6C1.10294 6.89997 0.699997 6.49703 0.699997 5.99998Z'
        fill='#4E5D78'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.699997 10.5C0.699997 10.0029 1.10294 9.59997 1.6 9.59997H12.4C12.8971 9.59997 13.3 10.0029 13.3 10.5C13.3 10.997 12.8971 11.4 12.4 11.4H1.6C1.10294 11.4 0.699997 10.997 0.699997 10.5Z'
        fill='#4E5D78'
      />
    </>
  ),
  defaultProps: {
    width: '18px',
    height: '18px',
    fill: 'none',
  },
})

export const UploadCloudIcon = createIcon({
  displayName: 'UploadCloudIcon',
  viewBox: '0 0 20 20',
  path: (
    <path
      d='M6.66602 13.3333L9.99935 10M9.99935 10L13.3327 13.3333M9.99935 10V17.5M16.666 13.9524C17.6839 13.1117 18.3327 11.8399 18.3327 10.4167C18.3327 7.88536 16.2807 5.83333 13.7493 5.83333C13.5673 5.83333 13.3969 5.73833 13.3044 5.58145C12.2177 3.73736 10.2114 2.5 7.91602 2.5C4.46424 2.5 1.66602 5.29822 1.66602 8.75C1.66602 10.4718 2.36222 12.0309 3.48847 13.1613'
      strokeWidth='1.66667'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  ),
  defaultProps: {
    width: '1.6rem',
    height: '1.6rem',
    fill: 'none',
    stroke: 'primary.200',
  },
})
