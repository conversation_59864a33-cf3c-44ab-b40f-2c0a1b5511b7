import Avatar from '@/components/Avatar'
import { ClockIcon, SimpleArrowIcon } from '@/components/EbanaIcons'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import useDatePicker from '@/hooks/datePicker'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Grid, Stack, Text } from '@chakra-ui/react'

Calendar.fragment = graphql(`
  fragment Calendar on Calendar {
    events {
      __typename
      ... on GovernanceMeetingCalendarEvent {
        id
        title
        company {
          tradeName
          logo {
            url
          }
        }
        date {
          relativeShort
          relativeSeconds
          time {
            default
          }
          date {
            default
          }
          month {
            label
          }
          year
          monthOfYear
          dayOfMonth
        }
      }
    }
  }
`)

export default function Calendar({ data }: { data: FragmentOf<typeof Calendar.fragment> }) {
  const { t, lng } = useEbanaLocale()
  const calendar = readFragment(Calendar.fragment, data)

  const {
    dayNames,
    monthDays,
    selectedYear,
    selectedMonth,
    setSelectedMonth,
    selectedDay,
    setSelectedDay,
    prevDays,
    setSelectedYear,
  } = useDatePicker(lng)

  const fullDayNames = [
    t('sunday'),
    t('monday'),
    t('tuesday'),
    t('wednesday'),
    t('thursday'),
    t('friday'),
    t('saturday'),
  ]

  const colors = {
    yellow: '#FEC412',
    blue: '#6490EB',
    red: '#FF5630',
  }

  const months = [
    { label: t('January'), value: 0 },
    { label: t('February'), value: 1 },
    { label: t('March'), value: 2 },
    { label: t('April'), value: 3 },
    { label: t('May'), value: 4 },
    { label: t('June'), value: 5 },
    { label: t('July'), value: 6 },
    { label: t('August'), value: 7 },
    { label: t('September'), value: 8 },
    { label: t('October'), value: 9 },
    { label: t('November'), value: 10 },
    { label: t('December'), value: 11 },
  ]

  const currentDate = new Date()
  const currentDay = currentDate.getDate()
  const currentDayOfWeek = fullDayNames[currentDate.getDay()]
  const currentMonth = currentDate.getMonth()

  const filteredEvents = calendar.events.filter(
    (event) =>
      event.date.dayOfMonth === Number(selectedDay) &&
      event.date.monthOfYear === Number(selectedMonth.value) + 1 &&
      event.date.year === selectedYear.value
  )

  const eventDays = monthDays.map(({ dayLabel }: any) => {
    const events = calendar.events.filter(
      (event) =>
        event.date.dayOfMonth === Number(dayLabel) &&
        event.date.monthOfYear === Number(selectedMonth.value) + 1 &&
        event.date.year === selectedYear.value
    )
    return events.map((event) => event.date.relativeSeconds)
  })

  const updateMonth = (direction: 'prev' | 'next') => {
    const current = Number(selectedMonth.value)
    let newMonth = direction === 'prev' ? current - 1 : current + 1
    let newYear = selectedYear.value

    if (newMonth < 0) {
      newMonth = 11
      newYear--
    } else if (newMonth > 11) {
      newMonth = 0
      newYear++
    }

    setSelectedYear({ label: newYear.toString(), value: newYear })
    setSelectedMonth({ label: months[newMonth].label, value: newMonth })
  }

  return (
    <Stack bg='white' borderRadius='14px' px='initial' py='1.25rem'>
      <Stack gap={0} mx='auto'>
        <Box color='primary.200' textStyle='body4' lineHeight={1}>
          {t('today')}
        </Box>
        <Box textStyle='body2' fontWeight={600}>
          {currentDay} {lng === 'en' ? months[currentMonth].label.slice(0, 3) : months[currentMonth].label},{' '}
          {currentDayOfWeek}
        </Box>
        <Flex dir='ltr' mt='1rem'>
          <Box onClick={() => updateMonth('prev')} {...arrowBoxProps}>
            <SimpleArrowIcon transform='rotate(180deg)' mt='-5px' width='18px' />
          </Box>
          <Box my='auto' mx='auto' fontWeight={600}>
            {selectedMonth.label} {selectedYear.label}
          </Box>
          <Box onClick={() => updateMonth('next')} {...arrowBoxProps}>
            <SimpleArrowIcon mt='-5px' width='18px' _rtl={{ transform: 'scaleX(1)' }} />
          </Box>
        </Flex>
        <Grid mt='1rem' templateColumns='repeat(7, 2.5em)' columnGap='0.375em' rowGap='0.3125em' mb='1.0625em'>
          {dayNames.map((day) => (
            <Box key={day} textAlign='center' color='typography.200' textStyle='tagSm'>
              {day}
            </Box>
          ))}
        </Grid>
        <Grid templateColumns='repeat(7, 2.5em)' columnGap='0.375em' rowGap='0.3125em'>
          {monthDays.map(({ dayLabel }: any, i) => {
            const isSelected = dayLabel == selectedDay
            const isPrevMonthDay = !dayLabel
            const day = isPrevMonthDay ? prevDays[i] : dayLabel < 10 ? dayLabel.charAt(1) : dayLabel
            return (
              <Stack
                key={i}
                _hover={{ bgColor: isPrevMonthDay ? 'white' : 'rgba(0, 191, 178, 0.10) !important' }}
                backgroundColor={isSelected ? 'rgba(0, 191, 178, 0.10)' : 'white'}
                borderWidth='1px'
                borderColor={isSelected ? 'primary.200' : 'stroke.100'}
                textAlign='center'
                height='40px'
                width='40px'
                cursor={isPrevMonthDay ? 'default' : 'pointer'}
                textStyle='body4'
                fontWeight={600}
                color={isPrevMonthDay ? 'typography.300' : '#00263A'}
                borderRadius='6px'
                gap='0'
                onClick={isPrevMonthDay ? undefined : () => setSelectedDay(dayLabel)}>
                <Box>{day}</Box>
                <Flex gap='2px' mx='auto'>
                  {eventDays[i]?.map((eventTime, index) => (
                    <Box
                      key={index}
                      borderRadius='50px'
                      bg={Number(eventTime) < 0 ? colors.red : Number(eventTime) < 86400 ? colors.yellow : colors.blue}
                      height='8px'
                      width='8px'
                      mt='2px'
                    />
                  ))}
                </Flex>
              </Stack>
            )
          })}
        </Grid>
      </Stack>
      {filteredEvents.map((event, index) => {
        const secs = Number(event.date.relativeSeconds)
        const mainColor = secs < 0 ? colors.red : secs < 86400 ? colors.yellow : colors.blue
        const chipBg = `${mainColor}1A`
        return (
          <Flex key={index} mt='1rem' gap='1rem'>
            <Box w='4px' bg={mainColor} borderRadius='2px' />
            <Stack gap='.5rem' py='.4rem' flex='1'>
              <Flex align='center' bg={chipBg} px='10px' py='3px' borderRadius='8px' w='fit-content' gap='0.5rem'>
                <ClockIcon stroke={mainColor} />
                <Text color='#00263A'>{event.date.time.default}</Text>
              </Flex>
              <Text textStyle='body3' color='typography.100' fontWeight={600}>
                {event.title}
              </Text>
              <Flex align='center' gap='0.75rem'>
                <Avatar
                  boxSize='25px'
                  name={event.company?.tradeName || event.title}
                  src={event.company?.logo?.url || '/assets/logo_in_circle.svg'}
                  bg='white'
                />
                <Text textStyle='body4' fontWeight='600' color='typography.100'>
                  {event.company?.tradeName || event.title}
                </Text>
              </Flex>
            </Stack>
          </Flex>
        )
      })}
    </Stack>
  )
}

const arrowBoxProps = {
  maxH: '40px',
  maxW: '40px',
  backgroundColor: 'white',
  borderWidth: '1px',
  borderColor: 'stroke.100',
  borderStyle: 'solid',
  textAlign: 'center',
  height: '40px',
  width: '40px',
  cursor: 'pointer',
  textStyle: 'body4',
  fontWeight: 500,
  color: '#00263A',
  borderRadius: '6px',
  alignContent: 'center',
}
