import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, FileUpload as ChakraFileUpload, Circle, FileUpload, Flex } from '@chakra-ui/react'
import React from 'react'
import { DeleteIcon, UploadCloudIcon } from '../EbanaIcons'
import { DownloadFile, MIMETypeIcon } from '../file'

export const FileUploadRootProvider = React.forwardRef<HTMLDivElement, ChakraFileUpload.RootProviderProps>(
  function FileUploadRootProvider(props, ref) {
    return (
      <ChakraFileUpload.RootProvider
        ref={ref}
        style={{
          margin: '-70px 0',
          padding: 0,
        }}
        alignItems='stretch'
        {...props}>
        <ChakraFileUpload.HiddenInput />
        {props.children}
      </ChakraFileUpload.RootProvider>
    )
  }
)

export const FileUploadDropZone = React.forwardRef<
  HTMLDivElement,
  { maxSize?: string } & ChakraFileUpload.DropzoneProps
>(function FileUploadDropZone(props, ref) {
  const { t } = useEbanaLocale()

  const { maxSize, ...rest } = props

  return (
    <ChakraFileUpload.Dropzone ref={ref} gap='0.8em' className={`${props.className} group`} {...rest}>
      <Circle
        borderWidth='8px'
        borderColor='#E5F9F7'
        p='0.6rem'
        bg='white'
        transition='all 0.3s ease-in-out'
        _groupHover={{
          borderColor: 'background.300',
          backgroundColor: '#EAECF0',
        }}
        _groupDisabled={{
          borderWidth: '0',
          backgroundColor: '#EAECF0',
        }}
        css={{
          '.group:is([data-dragging]):not(:disabled, [data-disabled]) &': {
            borderColor: 'background.300',
            backgroundColor: '#EAECF0',
          },
        }}>
        <UploadCloudIcon _groupDisabled={{ stroke: 'typography.300' }} />
      </Circle>
      <ChakraFileUpload.DropzoneContent>
        <Flex gap='0.25rem'>
          <Box color='typography.100' textStyle='body4'>
            {t('drag_file_title')}
          </Box>
          <Box
            color='primary.200'
            textStyle='body4'
            fontWeight={600}
            _groupDisabled={{
              color: 'typography.300',
            }}>
            {t('choose_file')}
          </Box>
        </Flex>
        <Box color='typography.100' fontWeight={600} textStyle='body5'>
          {t('variable_file_maximum_size', { maxSize: maxSize || '10 MB' })}
        </Box>
      </ChakraFileUpload.DropzoneContent>
    </ChakraFileUpload.Dropzone>
  )
})

export const FileUploadItem = React.forwardRef<
  HTMLLIElement,
  { withDelete?: boolean; withDownload?: boolean } & ChakraFileUpload.ItemProps
>(function FileUploadItem(props, ref) {
  const { withDelete = false, withDownload = true, children, ...rest } = props

  return (
    <ChakraFileUpload.Item ref={ref} {...rest}>
      <FileUpload.ItemPreview>
        <MIMETypeIcon type={props.file.type} />
      </FileUpload.ItemPreview>
      {props.children}
      <Flex alignItems='center' gap='0.5em'>
        {withDelete && (
          <FileUpload.ItemDeleteTrigger>
            <DeleteIcon />
          </FileUpload.ItemDeleteTrigger>
        )}
        {withDownload && (
          <DownloadFile
            name={props.file.name}
            url={URL.createObjectURL(new Blob([props.file], { type: props.file.type }))}
          />
        )}
      </Flex>
    </ChakraFileUpload.Item>
  )
})

export const FileUploadItemContent = React.forwardRef<HTMLDivElement, ChakraFileUpload.ItemContentProps>(
  function FileUploadItemContent(props, ref) {
    return (
      <ChakraFileUpload.ItemContent ref={ref} {...props}>
        <ChakraFileUpload.ItemName />
        <ChakraFileUpload.ItemSizeText />
      </ChakraFileUpload.ItemContent>
    )
  }
)
