import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Stack } from '@chakra-ui/react'
import { EbanaDarkIcon, EbanaLogoTextIconAr, EbanaLogoTextIconEn } from '../EbanaIcons'

export default function EbanaWithProfileType({ profileType }: { profileType: string }) {
  const { t, lng } = useEbanaLocale()

  return (
    <Flex position='relative' maxWidth='177px' h='50px' gap='9px'>
      <EbanaDarkIcon h='50px' w='50px' />
      <Stack justifyContent='start' alignContent='start' width='fit-content'>
        {lng === 'en' ? (
          <EbanaLogoTextIconEn maxW='fit-content' maxH='16px' me='auto' mt='10px' />
        ) : (
          <EbanaLogoTextIconAr w='fit-content' maxH='22px' me='auto' mt='10px' />
        )}
        <Box color='#00263A' textStyle='body5' position='absolute' top='2rem' insetStart='3.5rem'>
          {t(profileType)}
        </Box>
      </Stack>
    </Flex>
  )
}
