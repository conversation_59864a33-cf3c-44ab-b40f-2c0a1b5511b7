import { FieldStatus } from '@/graphql/fragments'
import { Box, Field as ChakraField, Flex } from '@chakra-ui/react'
import React from 'react'
import { useFormContext } from 'react-hook-form'
import { TooltipFilledIcon } from '../EbanaIcons'
import { Tooltip, TooltipProps } from '../ui/tooltip'

export type FieldType = {
  label: string
  path: string
  status: FieldStatus
  statusMessage?: string
  helperText?: string
  tooltip?: string
  pattern?: string
  patternMessage?: string
}
const FieldContext = React.createContext<FieldType>(null)

export function useField(): FieldType {
  return React.useContext(FieldContext)
}

type FieldProps = {
  field: FieldType
  indices?: number[]
} & ChakraField.RootProps

function Root({ field, indices = [], ...rest }: FieldProps) {
  const { status } = field

  const methods = useFormContext()
  const { formState } = methods

  let resolvedPath = field.path
  if (indices.length) {
    for (const i of indices) {
      resolvedPath = resolvedPath.replace('$', i.toString())
    }
  }

  return (
    <FieldContext.Provider value={{ ...field, path: resolvedPath }}>
      <ChakraField.Root
        invalid={formState.errors != null && Object.keys(formState.errors).length > 0}
        required={status === 'REQUIRED'}
        disabled={status === 'DISABLED'}
        readOnly={status === 'UNAVAILABLE'}
        {...rest}
      />
    </FieldContext.Provider>
  )
}

function Label({ withIndicator = true, children, ...rest }: { withIndicator?: boolean } & ChakraField.LabelProps) {
  const { status } = useField()

  const showRequiredIndicator = withIndicator ? status === 'REQUIRED' : false

  return (
    <ChakraField.Label {...rest}>
      {children}
      {showRequiredIndicator && <ChakraField.RequiredIndicator />}
    </ChakraField.Label>
  )
}

function HelperText(props: ChakraField.HelperTextProps) {
  const { helperText } = useField()

  return <ChakraField.HelperText {...props}>{helperText}</ChakraField.HelperText>
}

export const Field = {
  Root,
  Label,
  HelperText,
}

type SimpleFieldProps = {
  withLabel?: boolean
  labelProps?: ChakraField.LabelProps
  withTooltip?: boolean
  tooltipProps?: Partial<TooltipProps>
  withHelperText?: boolean
  helperTextProps?: ChakraField.HelperTextProps
} & FieldProps
export function SimpleField({
  field,
  indices = [],
  withLabel = true,
  labelProps,
  withTooltip = false,
  tooltipProps,
  withHelperText = false,
  helperTextProps,
  children,
  ...rest
}: SimpleFieldProps) {
  return (
    <Field.Root field={field} {...rest}>
      <Flex>
        {withLabel && <Field.Label {...labelProps}>{field.label}</Field.Label>}
        {withTooltip && (
          <Tooltip content={field.tooltip} {...tooltipProps}>
            <Box>
              <TooltipFilledIcon />
            </Box>
          </Tooltip>
        )}
      </Flex>
      {children}
      {withHelperText && <Field.HelperText {...helperTextProps} />}
    </Field.Root>
  )
}
