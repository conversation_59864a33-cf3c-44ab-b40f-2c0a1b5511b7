import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Stack, useBreakpointValue } from '@chakra-ui/react'
import { usePathname } from 'next/navigation'
import React from 'react'
import { Sidebar, sidebarClasses } from 'react-pro-sidebar'
import { EbanaDarkIcon, EbanaLogoTextIconAr, EbanaLogoTextIconEn } from '../EbanaIcons'
import Feedback from '../header/Feedback'
import Profile from '../profile'
import NavigationMenu from './NavigationMenu'

export default function MobileNavigation({ toggled, setToggled }) {
  const { t, lng } = useEbanaLocale()

  const pathname = usePathname()

  const [profileExpanded, setProfileExpanded] = React.useState(false)

  const defaultWidth = useBreakpointValue(
    {
      base: '80%',
      sm: '50%',
    },
    {
      fallback: 'base',
    }
  )

  const renderedNavigationMenu = (
    <Box paddingEnd='1em'>
      <Box as='header' width='9.75em' height='4.75em' mb='1.25em'>
        <Flex position='relative' maxWidth='177px' h='44px' gap='7px'>
          <EbanaDarkIcon h='44px' w='44px' />
          <Stack justifyContent='start' alignContent='start' width='fit-content'>
            {lng === 'en' ? (
              <EbanaLogoTextIconEn maxW='fit-content' maxH='13px' me='auto' mt='8px' />
            ) : (
              <EbanaLogoTextIconAr w='fit-content' maxH='21px' me='auto' mt='7px' />
            )}
            <Box textStyle='body5' position='absolute' top='25px' insetStart='51px'>
              {t('company_profile')}
            </Box>
          </Stack>
        </Flex>
      </Box>
      <NavigationMenu collapsed={profileExpanded} />
    </Box>
  )

  return (
    <Sidebar
      rtl={lng == 'ar'}
      breakPoint='all'
      backgroundColor='#fff'
      transitionDuration={500}
      // width={toggled ? defaultWidth : 0}
      toggled={toggled}
      onBackdropClick={() => setToggled(false)}
      rootStyles={{
        [`.${sidebarClasses.container}`]: {
          border: toggled ? '1px solid #F2F2F4 !important' : 'none',
        },
        [`.${sidebarClasses.backdrop}`]: {
          cursor: 'auto',
          background: 'rgba(50, 59, 65, 0.6)',
        },
      }}>
      {toggled && (
        <Stack p='1em' justifyContent='space-between' h='100%'>
          {!profileExpanded && renderedNavigationMenu}
          <Box borderTopWidth={profileExpanded ? '0' : '1px'} borderTopColor='stroke.100' width='100%'>
            <Profile isOpen={profileExpanded} onToggle={() => setProfileExpanded((prevState) => !prevState)} />
            {!profileExpanded && <Feedback isMobile={true} />}
          </Box>
        </Stack>
      )}
    </Sidebar>
  )
}
