import { useAuth } from "@/context/new-auth"
import { useEbanaLocale } from "@/hooks/useEbanaTranslations"
import { Box, Flex, IconButton, Link, Stack } from "@chakra-ui/react"
import { route, RouteLiteral } from "nextjs-routes"
import React from "react"
import { Sidebar, sidebarClasses } from "react-pro-sidebar"
import { EbanaDarkIcon, MenuIcon } from "../EbanaIcons"
import EbanaWithProfileType from "../header/EbanaWithProfileType"
import NavigationMenu from "./NavigationMenu"
import Ad from "./ad"

export default function DesktopNavigation() {
  const { lng, t } = useEbanaLocale()
  const { activeWorkspace } = useAuth()

  const [collapsed, setCollapsed] = React.useState(false)
  const sidebarWidth = collapsed ? '5em' : '16.5em'

  const handleToggleCollapse = () => setCollapsed((prev) => !prev)

  type WorkspaceType = typeof activeWorkspace.type

  const renderLogoWithMenu = () => {
    const types: Record<WorkspaceType, { href: RouteLiteral; profileType: string }> = {
      company: {
        href: route({ pathname: '/[lng]/company/[companyId]/home', query: { lng, companyId: activeWorkspace.id } }),
        profileType: t('company_profile'),
      },
      as: {
        href: route({ pathname: '/[lng]/as/[encodedId]/home', query: { lng, encodedId: activeWorkspace.id } }),
        profileType: t('investor_mode'),
      },
      'as-company': {
        href: route({ pathname: '/[lng]/as/[encodedId]/home', query: { lng, encodedId: activeWorkspace.id } }),
        profileType: t('investor_mode'),
      },
      individual: {
        href: route({ pathname: '/[lng]/individual/home', query: { lng } }),
        profileType: t('individual'),
      },
    }
    const { href, profileType } = types[activeWorkspace.type as WorkspaceType]

    return (
      <>
        {collapsed && (
          <Box width='fit-content' height='2.25em' mx='auto'>
            <Link href={href}>
              <EbanaDarkIcon height='2.25em' mx='auto' />
            </Link>
          </Box>
        )}

        <Flex
          as='header'
          alignItems='center'
          justifyContent={collapsed ? 'center' : 'space-between'}
          px={collapsed ? 0 : '1.25em'}>
          {!collapsed && (
            <Link href={href}>
              <EbanaWithProfileType profileType={profileType} />
            </Link>
          )}
          <IconButton
            variant='plain'
            mt='1em'
            onClick={handleToggleCollapse}
            background='transparent'
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
            <MenuIcon />
          </IconButton>
        </Flex>
      </>
    )
  }

  return (
    <Sidebar
      rtl={lng === 'ar'}
      customBreakPoint='48em'
      breakPoint='all'
      backgroundColor='#EAECF0'
      transitionDuration={500}
      width={sidebarWidth}
      rootStyles={{
        [`.${sidebarClasses.container}`]: {
          width: sidebarWidth,
          position: 'fixed',
          top: 0,
          bottom: 0,
          overflow: 'hidden',
        },
        [`.${sidebarClasses.backdrop}`]: {
          cursor: 'auto',
          background: 'rgba(50, 59, 65, 0.6)',
        },
      }}>
      <Stack
        bg='white'
        borderRadius='14px'
        m='0.5rem'
        me='0px'
        h='calc(100vh - 1rem)'
        pe='0.25em'
        pt='1.4375em'
        justifyContent='space-between'
        maxHeight='100%'>
        {renderLogoWithMenu()}
        <Box h='100%' overflowY='auto' mt='1.75em'>
          <Stack alignItems={collapsed ? 'center' : 'stretch'} h='100%'>
            <Box>
              <NavigationMenu collapsed={collapsed} />
            </Box>
          </Stack>
        </Box>

        {!collapsed && <Ad />}
      </Stack>
    </Sidebar>
  )
}