import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Image, Stack } from '@chakra-ui/react'
import Link from 'next/link'
import { route } from 'nextjs-routes'
import React from 'react'

export function RegisterNewCompany({ isShort }: { isShort?: boolean }) {
  const { t, lng } = useEbanaLocale()
  const [loading, setLoading] = React.useState(false)

  if (isShort)
    return (
      <Box position='relative' bg='#1A2234' w='224px' h='fit-content' borderRadius='14px' overflow='hidden'>
        <Image position='absolute' width='100%' src='/assets/add-company-layer1.png' alt='Background' />
        <Image position='absolute' height='100%' src='/assets/add-company-layer2.png' alt='Background' />
        <Stack p='1.25rem' position='relative' width='100%' height='100%' color='white'>
          <Box lineHeight='1.5rem' fontWeight='semibold' textStyle='body3'>
            {t('register_new_company')}
          </Box>
          <Box lineHeight='1rem' textStyle='body5' fontWeight={500}>
            {t('register_new_company_description')}
          </Box>
          <Button
            mt='0.5rem'
            height='fit-content'
            asChild
            onClick={() => setLoading(true)}
            loading={loading}
            py='0.35rem'
            borderRadius='12px'
            w='100%'
            bg='white'
            color='#00263A'>
            <Link href={route({ pathname: '/[lng]/new-company', query: { lng } })}>{t('register_now')}</Link>
          </Button>
        </Stack>
      </Box>
    )
  else
    return (
      <Box position='relative' bg='#1A2234' w='224px' h='224px' borderRadius='14px' overflow='hidden'>
        <Image position='absolute' width='100%' src='/assets/add-company-layer1.png' alt='Background' />
        <Image position='absolute' height='100%' src='/assets/add-company-layer2.png' alt='Background' />
        <Stack p='1.25rem' position='relative' width='100%' height='100%' color='white'>
          <Box lineHeight='1.5rem' fontWeight='semibold' textStyle='body2'>
            {t('register_new_company')}
          </Box>
          <Box lineHeight='1rem' textStyle='body5' fontWeight={500}>
            {t('register_new_company_description')}
          </Box>
          <Button
            mt='auto'
            height='fit-content'
            asChild
            onClick={() => setLoading(true)}
            loading={loading}
            py='0.65rem'
            w='100%'
            bg='white'
            color='#00263A'>
            <Link href={route({ pathname: '/[lng]/new-company', query: { lng } })}>{t('register_now')}</Link>
          </Button>
        </Stack>
      </Box>
    )
}
