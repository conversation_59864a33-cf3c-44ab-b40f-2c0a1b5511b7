import { useAuth } from "@/context/new-auth"
import { Stack } from "@chakra-ui/react"
import MobileAd from "./MobileAd"
import { RegisterNewCompany } from "./NewRegisterCompany"
import OldRegisterCompany from "./OldRegisterCompany"

export default function Ad() {
  const { hasFlag, activeWorkspace } = useAuth()

  if (hasFlag('dynamic-dashboard'))
    return (
      <>
        <Stack gap='24px' w='fit-content' mx='auto' mb='1.4375em'>
          {activeWorkspace.type === 'individual' && <MobileAd />}
          <RegisterNewCompany isShort={window.innerHeight < 1005 || window.visualViewport?.height < 1005} />
        </Stack>
      </>
    )
  else return <OldRegisterCompany />
}