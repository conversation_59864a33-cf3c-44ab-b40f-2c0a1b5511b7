import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Flex, Image, Stack } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'

export default function OldRegisterCompany() {
  const { t, lng } = useEbanaLocale()
  const router = useRouter()

  return (
    <Stack
      background='#fff'
      width='100%'
      height='9em'
      position='relative'
      borderRadius='20px'
      alignItems='center'
      justifyContent='center'>
      <Image src='/assets/adCompany.svg' width='70%' height='90%' alt='' align='top' />

      <Stack position='absolute' mt='2em' borderRadius='14px'>
        <Button
          mx='auto'
          p={0}
          my={0}
          width='fit-content'
          maxHeight='fit-content'
          bg='transparent'
          id='registerCompany'
          onClick={() => router.push(route({ pathname: '/[lng]/new-company', query: { lng } }))}>
          <Box
            bg='primary.200'
            padding={0}
            alignContent='center'
            borderRadius='50%'
            height='3em'
            minHeight='3em'
            maxHeight='3em'
            width='3em'
            minWidth='3em'
            maxWidth='3em'
            alignSelf='center'
            zIndex={2}
            transform='translateY(1em)'
            boxShadow='0px 4px 6px -2px rgba(16, 24, 40, 0.1), 0px 12px 16px -4px rgba(16, 24, 40, 0.1)'>
            <Image mx='auto' height='1.25em' width='1.25em' src='/assets/add.svg' alt='' />
          </Box>
        </Button>

        <Flex
          mt='0.5em'
          width='100%'
          backgroundColor='#fff'
          borderRadius='14px'
          justifyContent='center'
          borderWidth='1px'
          borderColor='transparent'>
          <Box fontWeight={600} color='primary.200' alignSelf='center' display='inline-block'>
            {t('register_new_company')}
          </Box>
        </Flex>
      </Stack>
    </Stack>
  )
}
