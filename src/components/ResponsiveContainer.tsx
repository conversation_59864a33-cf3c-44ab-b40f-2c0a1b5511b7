import { Box, Flex, Stack } from '@chakra-ui/react'

export default function ResponsiveContainer({
  topContent,
  mainContent,
  sideContent,
}: {
  topContent?: React.ReactNode
  mainContent: React.ReactNode
  sideContent: React.ReactNode
}) {
  return (
    <Stack
      maxW='1156px'
      px={{ base: '1rem', md: '30px' }}
      mx={{ base: 'unset', md: 'auto' }}
      mt={{ base: '1rem', md: '0px' }}
      mb='1rem'
      gap={{ base: '1rem', md: '1.875rem' }}>
      {topContent && <Box>{topContent}</Box>}
      <Flex
        flexDir={{ base: 'column', xl: 'row' }}
        justifyContent='space-between'
        flexWrap={{ base: 'wrap', md: 'nowrap' }}
        gap={{ base: '1rem', md: '2rem' }}>
        <Stack maxW={{ base: '100%', xl: 'calc(100% - 382px)' }} gap='2rem'>
          {mainContent}
        </Stack>
        <Stack mb='1.4375em' gap='2rem' w={{ base: '100%', md: '350px' }}>
          {sideContent}
        </Stack>
      </Flex>
    </Stack>
  )
}
