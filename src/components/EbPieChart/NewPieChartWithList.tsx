import PieChartContainer from '@/components/EbPieChart'
import { ApplicationPropsContext } from '@/context/application-props'
import { Box, Flex, Stack } from '@chakra-ui/react'
import React from 'react'
import { v4 as uuidv4 } from 'uuid'

export default function Pie<PERSON>hartWithList(props) {
  const { title, data, colors, width = 200, height = 200, customTitle = 'Total issued shares', customValue } = props

  const { lng } = React.useContext(ApplicationPropsContext)

  const chartColors = colors || ['#6490EB', '#00BFB2', '#FEC412', '#924EF8', '#FF5630', '#003654']

  return (
    <Stack height='100%' color='#323B41'>
      <Flex
        alignItems='center'
        direction={{ base: 'column', md: 'row' }}
        flexWrap='nowrap'
        my='auto'
        justifyContent='center'>
        {/* Pie Chart */}
        <Box w={{ base: 'min-content', md: '40%' }}>
          <PieChartContainer
            lng={lng}
            variant='thin'
            data={data}
            dataKey='value'
            colors={chartColors}
            width={width}
            height={height}
            customTitle={customTitle}
            customValue={customValue}
          />
        </Box>

        {/* Legend */}
        <Stack
          w={{ base: '100%', md: '60%' }}
          p='1rem'
          alignItems='center'
          flex={{ base: '1', md: 'none' }}
          gap='0.75em'>
          {data?.map(({ label, displayedValue }, index) => {
            const color = chartColors[index % chartColors.length]

            return (
              <Flex key={uuidv4()} align='center' gap='0.75rem' px='0.25rem' justifyContent='space-between' w='100%'>
                {/* Colored bar */}
                <Box w='10px' h='4px' bg={color} borderRadius='full' flexShrink={0} />

                {/* Label */}
                <Box flex='1' fontWeight={500} fontSize='1rem' color='#00263A' lineHeight='1.25' borderRadius='50px'>
                  {label}
                </Box>

                {/* Value */}
                <Box fontSize='1rem' fontWeight={500} color={color} flexShrink={0} textAlign='right'>
                  {displayedValue}
                </Box>
              </Flex>
            )
          })}
        </Stack>
      </Flex>
    </Stack>
  )
}
