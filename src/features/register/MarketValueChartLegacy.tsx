import { RiyalNewSignIcon } from '@/components/EbanaIcons'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Stack } from '@chakra-ui/react'
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

MarketValueChartLegacy.registerFragment = graphql(`
  fragment MarketValueChartLegacy on ShareholderRegister {
    ticker {
      weekly {
        close {
          formatted
          value
        }
        start {
          short
        }
        end {
          date {
            medium
            iso
          }
        }
      }
    }
  }
`)

MarketValueChartLegacy.equityFragment = graphql(`
  fragment EquityMarketValueChartLegacy on EquityPositionRegister {
    ticker {
      weekly {
        close {
          formatted
          value
        }
        start {
          short
        }
        end {
          date {
            medium
            iso
          }
        }
      }
    }
  }
`)

type MarketValueChartLegacyProps = {
  withXAxis?: boolean
  withYAxis?: boolean
  withCartesianGrid?: boolean
  color?: string
  chartHeight?: number
}
export default function MarketValueChartLegacy({
  equityData,
  registerData,
  withXAxis = true,
  withYAxis = true,
  withCartesianGrid = true,
  color = '#00BFB2',
  chartHeight,
}: {
  equityData?: FragmentOf<typeof MarketValueChartLegacy.equityFragment>
  registerData?: FragmentOf<typeof MarketValueChartLegacy.registerFragment>
} & MarketValueChartLegacyProps) {
  const { t, lng } = useEbanaLocale()

  const equityResult = readFragment(MarketValueChartLegacy.equityFragment, equityData)
  const registerResult = readFragment(MarketValueChartLegacy.registerFragment, registerData)
  const { ticker } = equityResult || registerResult

  if (ticker == null || ticker.weekly.length === 0) {
    return
  }

  const mappedData = []
  const dateMap = new Map()

  ticker.weekly.forEach((c) => {
    dateMap.set(new Date(c.end.date.iso).toISOString().split('T')[0], c.close.value)
  })

  const startDate = new Date(ticker.weekly[0].end.date.iso)
  const endDate = new Date(ticker.weekly[ticker.weekly.length - 1].end.date.iso)

  while (startDate <= endDate) {
    const dateString = startDate.toISOString().split('T')[0]
    mappedData.push({
      x: new Date(dateString).toLocaleDateString(lng === 'en' ? 'en-GB' : 'ar-EG-u-nu-latn', {
        day: '2-digit',
        month: 'short',
      }),
      y: dateMap.get(dateString) || (mappedData.length > 0 ? mappedData[mappedData.length - 1].y : 0),
    })
    startDate.setDate(startDate.getDate() + 1)
  }

  return (
    <ResponsiveContainer minWidth='400' height={chartHeight ? chartHeight : 544}>
      <AreaChart
        data={mappedData}
        width={400}
        height={chartHeight ? chartHeight : 400}
        margin={{ top: 5, right: 0, left: 0, bottom: 60 }}>
        {withCartesianGrid && <CartesianGrid vertical={false} strokeDasharray='3 1' />}
        {withYAxis && (
          <YAxis
            tick={({ x, y, payload }) => {
              const renderedText = (
                <text x={0} y={0} textAnchor='end' fontSize='1rem' fontWeight={500} fill='#4E5D78'>
                  {payload.value}
                </text>
              )
              if (lng === 'ar') {
                return <g transform={`translate(${x + 5},${y})`}>{renderedText}</g>
              }
              return <g transform={`translate(${x},${y})`}>{renderedText}</g>
            }}
            orientation={lng === 'ar' ? 'right' : 'left'}
          />
        )}
        {withXAxis && (
          <XAxis
            tick={({ x, y, payload }) => {
              const renderedText = (
                <text x={0} y={10} dy={16} textAnchor='start' fontSize='12px' fontWeight={500} fill='#4E5D78'>
                  {payload.value}
                </text>
              )
              if (lng === 'ar') {
                return <g transform={`translate(${x - 20},${y + 5}) rotate(-90)`}>{renderedText}</g>
              }
              return <g transform={`translate(${x - 23},${y + 70}) rotate(-90)`}>{renderedText}</g>
            }}
            dataKey='x'
            interval={7}
            axisLine={false}
            tickLine={false}
            reversed={lng === 'ar'}
          />
        )}
        <Tooltip
          allowEscapeViewBox={{ x: false, y: true }}
          cursor={false}
          content={({ payload }) => {
            return (
              <Stack gap={0}>
                <Stack
                  gap={0}
                  width='12rem'
                  height='4.915rem'
                  bg='#00263A80'
                  backdropFilter='blur(10px)'
                  color='white'
                  fontWeight={700}
                  borderRadius='0.5rem'>
                  <Flex
                    px='0.75rem'
                    fontWeight={600}
                    alignContent='center'
                    justifyContent='space-between'
                    bg='rgba(255, 255, 255, 0.20)'
                    borderTopRadius='0.5rem'
                    height='50%'
                    width='100%'>
                    <Box width='fit-content' my='auto'>
                      {t('selected_date')}
                    </Box>
                    <Box width='fit-content' my='auto'>
                      {payload[0]?.payload.x}
                    </Box>
                  </Flex>
                  <Flex
                    px='0.75rem'
                    fontWeight={400}
                    alignContent='center'
                    justifyContent='space-between'
                    height='50%'
                    width='100%'>
                    <Box width='fit-content' my='auto'>
                      {t('share_price')}
                    </Box>
                    <Box dir='ltr' width='fit-content' my='auto'>
                      <RiyalNewSignIcon fill='white'/> {payload[0]?.payload.y}
                    </Box>
                  </Flex>
                </Stack>
              </Stack>
            )
          }}
        />
        <Area type='monotone' dataKey='y' stroke={color} strokeWidth='4' fill='url(#g5)' />
        <defs>
          <linearGradient id='g5' x2='0' y2='1.4'>
            <stop stopColor={color} stopOpacity='0.5' />
            <stop offset='0.666' stopColor={color} stopOpacity='0' />
          </linearGradient>
        </defs>
      </AreaChart>
    </ResponsiveContainer>
  )
}