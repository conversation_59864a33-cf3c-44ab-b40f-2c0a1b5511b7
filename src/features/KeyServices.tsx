import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Image, Stack, Tabs } from '@chakra-ui/react'

export function KeyServices() {
  const { t } = useEbanaLocale()

  return (
    <Stack>
      <Box textStyle='body1' fontWeight={600} color='#121423'>
        {t('ad_key_services')}
      </Box>
      <Tabs.Root defaultValue='0' mt='1rem' flexDirection='column' gap={{ base: '1em', md: '2em' }} width='100%'>
        <Box
          maxW='100%'
          overflow='hidden'
          css={{
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': { display: 'none' },
            overflow: 'hidden',
          }}>
          <Tabs.List maxWidth='714px' justifyContent='space-between' gap='12px'>
            <Tabs.Trigger
              value='0'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              minW='274px'
              w='274px'
              _before={{ display: 'none' }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} mx='auto'>
                {t('organizing_meetings_and_decisions')}
              </Box>
            </Tabs.Trigger>
            <Tabs.Trigger
              value='1'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              minW='157px'
              w='157px'
              _before={{ display: 'none' }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} mx='auto'>
                {t('electronic_voting')}
              </Box>
            </Tabs.Trigger>
            <Tabs.Trigger
              value='2'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              minW='257px'
              w='257px'
              _before={{ display: 'none' }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} mx='auto'>
                {t('employee_stock_option_program')}
              </Box>
            </Tabs.Trigger>
          </Tabs.List>
        </Box>
        <Tabs.Content value='0' p={0}>
          <DetailsCard
            title={t('organizing_meetings_and_decisions')}
            subtitle={t('organizing_meetings_and_decisions_details')}
            img='/assets/Meeting-room.png'
            img2={undefined}
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
        <Tabs.Content value='1' p={0}>
          <DetailsCard
            title={t('electronic_voting')}
            subtitle={t('electronic_voting_details')}
            img='/assets/assembly-example1.png'
            img2='/assets/assembly-example2.png'
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
        <Tabs.Content value='2' p={0}>
          <DetailsCard
            title={t('employee_stock_option_program')}
            subtitle={t('employee_stock_option_program_details')}
            img='/assets/esop-management.png'
            img2={undefined}
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
      </Tabs.Root>
    </Stack>
  )
}

function DetailsCard({ title, subtitle, img, img2, color }) {
  const { t, lng } = useEbanaLocale()
  const percent = lng === 'ar' ? '60%' : '40%'
  return (
    <Stack
      position='relative'
      gap='1rem'
      p='30px'
      height='370px'
      mt='24px'
      borderRadius='14px'
      bg={`radial-gradient(circle at ${percent} 60%,
        ${color} 0%, rgba(255,255,255,1) 60%)`}>
      <Box textStyle='body1' fontWeight={600}>
        {t(title)}
      </Box>
      <Box textStyle='body4'>{t(subtitle)}</Box>
      <Box mt='auto' position='relative' mx='auto' maxWidth='269px' maxHeight='175px'>
        <Image src={img} alt='' />
        {img2 && <Image position='absolute' mt='-143px' ms='-4rem' maxHeight='112px' src={img2} alt='' />}
      </Box>
    </Stack>
  )
}
