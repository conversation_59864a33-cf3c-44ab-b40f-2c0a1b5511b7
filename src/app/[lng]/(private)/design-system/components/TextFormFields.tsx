import { CheckIcon, DeleteIcon, TooltipFilledIcon } from '@/components/EbanaIcons'
import { Field, FieldType, SimpleField } from '@/components/form2/FormField'
import { TextInput } from '@/components/form2/TextInput'
import { Tooltip } from '@/components/ui/tooltip'
import { Box, Button, Flex, IconButton, Stack } from '@chakra-ui/react'
import { FormProvider, useFieldArray, useForm } from 'react-hook-form'

const field: FieldType = {
  label: 'Name',
  status: 'REQUIRED',
  statusMessage: "It's required!!!",
  path: 'name',
  helperText: 'Your name',
  tooltip: 'Your full name',
}

const fieldArr: FieldType = {
  label: 'Name',
  status: 'REQUIRED',
  statusMessage: "It's required!!!",
  path: 'names',
  helperText: 'Your name',
  tooltip: 'Your full name',
}

const fieldItem: FieldType = {
  label: 'Name',
  status: 'REQUIRED',
  statusMessage: "It's required!!!",
  path: 'names.$.value',
  helperText: 'Your name',
  tooltip: 'Your full name',
}

export function SimpleTextFormFields() {
  const form = useForm()

  return (
    <FormProvider {...form}>
      <Stack gap='3em'>
        <Stack>
          <Box>Normal</Box>
          <SimpleField field={field}>
            <TextInput />
          </SimpleField>
        </Stack>
        <Stack>
          <Box>Without Label</Box>
          <SimpleField field={field} withLabel={false}>
            <TextInput />
          </SimpleField>
        </Stack>
        <Stack>
          <Box>With Helper Text</Box>
          <SimpleField field={field} withHelperText={true} helperTextProps={{ color: 'typography.100' }}>
            <TextInput />
          </SimpleField>
        </Stack>
        <Stack>
          <Box>With Tooltip</Box>
          <SimpleField field={field} withTooltip={true} tooltipProps={{ showArrow: false }}>
            <TextInput />
          </SimpleField>
        </Stack>
      </Stack>
    </FormProvider>
  )
}

export function TextFormFields() {
  const form = useForm()

  return (
    <FormProvider {...form}>
      <Stack gap='3em'>
        <Normal />
        <CustomLabel />
        <Stack>
          <Box>List of fields</Box>
          <ListFields />
        </Stack>
      </Stack>
    </FormProvider>
  )
}

function Normal() {
  return (
    <Field.Root field={field}>
      <Flex>
        <Field.Label>{field.label}</Field.Label>
        <Tooltip content={field.tooltip}>
          <Box>
            <TooltipFilledIcon />
          </Box>
        </Tooltip>
      </Flex>
      <TextInput />
      <Field.HelperText />
    </Field.Root>
  )
}

function CustomLabel() {
  return (
    <Field.Root field={field}>
      <Flex>
        <Field.Label>
          <Flex alignItems='center' gap='.5em'>
            <Box color='typography.100'>{field.label}</Box>
            <CheckIcon stroke='primary.200' width='0.625rem' />
          </Flex>
        </Field.Label>
        <Tooltip content={field.tooltip}>
          <Box>
            <TooltipFilledIcon />
          </Box>
        </Tooltip>
      </Flex>
      <TextInput />
      <Field.HelperText />
    </Field.Root>
  )
}

function ListFields() {
  const { fields, append, remove } = useFieldArray({ name: fieldArr.path })

  return (
    <Stack>
      <Button size='sm' alignSelf='flex-start' p='.5em 1.5em' onClick={() => append({ value: '' })}>
        Add
      </Button>
      {fields.map((f, index) => {
        return (
          <Flex key={f.id} gap='.5em'>
            <Field.Root field={fieldItem} indices={[index]}>
              <Flex>
                <Field.Label>{fieldItem.label}</Field.Label>
                <Tooltip content={fieldItem.tooltip}>
                  <Box>
                    <TooltipFilledIcon />
                  </Box>
                </Tooltip>
              </Flex>
              <TextInput />
              <Field.HelperText />
            </Field.Root>
            <IconButton variant='plain' onClick={() => remove(index)}>
              <DeleteIcon />
            </IconButton>
          </Flex>
        )
      })}
    </Stack>
  )
}
