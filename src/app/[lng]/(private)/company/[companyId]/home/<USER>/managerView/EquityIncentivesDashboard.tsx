import CardContainer from '@/components/CardContainer'
import { SearchIcon } from '@/components/EbanaIcons'
import Pie<PERSON>hartContainer from '@/components/EbPieChart'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { useOnClickOutside } from '@/hooks/useOnClickOutside'
import { Box, Button, Flex, Image, Input, Separator, Stack, Text, VStack } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React, { useRef } from 'react'
import { FaChevronDown } from 'react-icons/fa'
import { PiCheckLight } from 'react-icons/pi'
import { ResponsiveContainer } from 'recharts'

EquityIncentivesDashboard.fragment = graphql(`
  fragment EquityIncentivesDashboard on EquityIncentiveCompanyReports {
    totalShareCount {
      formatted
      value
    }
    grantsCount {
      formatted
      value
    }
    granteeCount {
      formatted
      value
    }
    grantedShares {
      ...NumberToTotal
    }
    availableShares {
      ...NumberToTotal
    }
    activeGrants {
      ...NumberToTotal
    }
    finishedGrants {
      ...NumberToTotal
    }
    programs {
      id
      name
      granteeCount {
        formatted
        value
      }
      grantsCount {
        formatted
        value
      }
      shareCount {
        formatted
        value
      }
      grantedShares {
        ...NumberToTotal
      }
      availableShares {
        ...NumberToTotal
      }
      finishedGrants {
        ...NumberToTotal
      }
      activeGrants {
        ...NumberToTotal
      }
      pendingGrants {
        ...NumberToTotal
      }
    }
  }
  fragment NumberToTotal on NumberToTotal {
    n {
      value
      formatted
    }
    p {
      value
      formatted
    }
  }
`)

export default function EquityIncentivesDashboard({
  data,
}: {
  data: FragmentOf<typeof EquityIncentivesDashboard.fragment>
}) {
  const { t, lng } = useEbanaLocale()
  const { id: companyId } = React.useContext(CompanyContext)
  const [showSearch, setShowSearch] = React.useState(false)
  const [search, setSearch] = React.useState('')
  const [selectedProgramId, setSelectedProgramId] = React.useState<string | null>(null)
  const dropDownRef = useRef<HTMLDivElement>(null)
  useOnClickOutside(dropDownRef, () => setShowSearch(false))
  const router = useRouter()

  function selectProgramId(id: string | null) {
    setSelectedProgramId(id)
    setShowSearch(false)
  }

  const programs =
    search.length > 0
      ? (data as any).programs.filter((p) => p.name.toLowerCase().includes(search.toLowerCase()))
      : (data as any).programs

  const selectedProgram = selectedProgramId ? (data as any).programs.find((p) => p.id === selectedProgramId) : null

  return (
    <CardContainer px={0} pt='1em' pb='0.5rem' borderRadius='14px' borderStyle='solid'>
      <Flex
        px={{ base: '1em', md: '2em' }}
        mb='1em'
        display='flex'
        justifyContent='space-between'
        gap={{ base: '1em', md: '0' }}>
        <Flex position='relative' onClick={() => setShowSearch(!showSearch)} cursor='pointer'>
          <Flex gap='10px' justifyContent='space-between' alignItems='center'>
            <Text fontSize={{ base: '1.1em', md: '1.3em' }} fontWeight={700} color='#121423' lineClamp={1} maxW='430px'>
              {!selectedProgramId ? t('all_equity_incentives') : selectedProgram?.name}
            </Text>
            <Box borderRadius='full' p={1} border='1px solid #F3F4F6'>
              <FaChevronDown size='14px' />
            </Box>
          </Flex>
          {showSearch && (
            <Stack
              ref={dropDownRef}
              position='absolute'
              width='14.875rem'
              bg='#fff'
              top='3rem'
              shadow='md'
              borderRadius='8px'
              zIndex={1000}>
              <Flex alignItems='center' px={2} borderBottom='1px solid #F3F4F6'>
                <SearchIcon />
                <Input
                  placeholder='Search program...'
                  border='none'
                  focusRing='none'
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Flex>
              <Stack overflowY='scroll' gap={0}>
                <Box
                  onClick={() => selectProgramId(null)}
                  p={2}
                  fontWeight='medium'
                  _hover={{ bg: '#F3F4F6' }}
                  cursor='pointer'
                  bg={selectedProgramId === null ? '#F3F4F6' : undefined}>
                  <Flex alignItems='center' justifyContent='space-between'>
                    <Box>{t('all_equity_incentives')}</Box>
                    {selectedProgramId === null && <PiCheckLight fill='#00BFB2' />}
                  </Flex>
                </Box>
                {programs.map((program) => (
                  <Box
                    onClick={() => selectProgramId(program.id)}
                    key={program.id}
                    p={2}
                    fontWeight='medium'
                    _hover={{ bg: '#F3F4F6' }}
                    cursor='pointer'
                    bg={selectedProgramId === program.id ? '#F3F4F6' : undefined}>
                    <Flex alignItems='center' justifyContent='space-between'>
                      <Box>{program.name}</Box>
                      {selectedProgramId === program.id && <PiCheckLight fill='#00BFB2' />}
                    </Flex>
                  </Box>
                ))}
              </Stack>
            </Stack>
          )}
        </Flex>
        <Button
          onClick={() => {
            router.push(
              route({
                pathname: '/[lng]/company/[companyId]/equity-incentive-grants/management',
                query: { lng, companyId },
              })
            )
          }}
          variant='secondary'
          px='1.5em'
          py='0.5em'
          _hover={{ bg: 'secondary.100' }}>
          {t('captable_tabs.see_more_details')}
        </Button>
      </Flex>

      <Separator borderColor='gray.200' />

      <Stack justify='space-between' pt='32px' px='28px' pb='24px' flexWrap='wrap'>
        <Flex justifyContent='space-between' flexDir={{ base: 'column', md: 'row' }} gap={{ base: '2rem', md: 0 }}>
          <StatCard
            image='/assets/total_stocks.svg'
            label={t('total_stocks')}
            value={
              selectedProgramId === null
                ? (data as any).totalShareCount.formatted
                : selectedProgram?.shareCount?.formatted
            }
          />
          <StatCard
            image='/assets/contracts.svg'
            label={t('contracts')}
            value={
              selectedProgramId === null ? (data as any).grantsCount.formatted : selectedProgram?.grantsCount?.formatted
            }
          />
          <StatCard
            image='/assets/grantees.svg'
            label={t('grantees')}
            value={
              selectedProgramId === null
                ? (data as any).granteeCount.formatted
                : selectedProgram?.granteeCount?.formatted
            }
          />
        </Flex>
        <Flex
          justifyContent='space-between'
          mt='2rem'
          flexDir={{ base: 'column', md: 'row' }}
          gap={{ base: '2rem', md: 0 }}>
          <Flex align='start' gap={4} alignItems='center'>
            <DonutChart
              data={[
                {
                  name: t('granted'),
                  value:
                    selectedProgramId === null
                      ? (data as any).grantedShares.n.value
                      : selectedProgram.grantedShares.n.value,
                  color: '#6366F1',
                },
                {
                  name: t('available'),
                  value:
                    selectedProgramId === null
                      ? (data as any).availableShares.n.value
                      : selectedProgram.availableShares.n.value,
                  color: '#00BFB2',
                },
              ]}
            />
            <Legend
              data={[
                {
                  name: t('granted'),
                  value:
                    selectedProgramId === null
                      ? (data as any).grantedShares.p.formatted
                      : selectedProgram.grantedShares.p.formatted,
                  color: '#6366F1',
                  count:
                    selectedProgramId === null
                      ? (data as any).grantedShares.n.value
                      : selectedProgram.grantedShares.n.value,
                },
                {
                  name: t('available'),
                  value:
                    selectedProgramId === null
                      ? (data as any).availableShares.p.formatted
                      : selectedProgram.availableShares.p.formatted,
                  color: '#00BFB2',
                  count:
                    selectedProgramId === null
                      ? (data as any).availableShares.n.value
                      : selectedProgram.availableShares.n.value,
                },
              ]}
            />
          </Flex>
          <Flex align='start' gap={4} alignItems='center'>
            <DonutChart
              data={[
                {
                  name: t('active'),
                  value:
                    selectedProgramId === null
                      ? (data as any).activeGrants.n.value
                      : selectedProgram.activeGrants.n.value,
                  color: '#00BFB2',
                },
                {
                  name: t('completed'),
                  value:
                    selectedProgramId === null
                      ? (data as any).finishedGrants.n.value
                      : selectedProgram.finishedGrants.n.value,
                  color: '#F59E0B',
                },
              ]}
            />
            <Legend
              data={[
                {
                  name: t('active'),
                  value:
                    selectedProgramId === null
                      ? (data as any).activeGrants.n.value
                      : selectedProgram.activeGrants.n.value,
                  color: '#00BFB2',
                },
                {
                  name: t('completed'),
                  value:
                    selectedProgramId === null
                      ? (data as any).finishedGrants.n.value
                      : selectedProgram.finishedGrants.n.value,
                  color: '#F59E0B',
                },
              ]}
            />
          </Flex>
        </Flex>
      </Stack>
    </CardContainer>
  )
}

function StatCard({ image, label, value }) {
  return (
    <Flex gap={2} align='start'>
      <Flex gap={3}>
        <Image src={image} alt={label} width='3rem' height='3rem' />
        <VStack align='start' gap={0}>
          <Box fontSize='sm' color='gray.500'>
            {label}:
          </Box>
          <Box fontSize='2xl' fontWeight='bold'>
            {value.toLocaleString()}
          </Box>
        </VStack>
      </Flex>
    </Flex>
  )
}

function DonutChart({ data, size = 120 }) {
  const chartColors = data.map((item) => item.color)
  return (
    <ResponsiveContainer width={size} height={size}>
      <PieChartContainer
        lng='ar'
        variant='thin'
        data={data}
        dataKey='value'
        colors={chartColors}
        innerRadius={50}
        outerRadius={60}
        showTotal={false}
      />
    </ResponsiveContainer>
  )
}

function Legend({ data }) {
  return (
    <VStack align='start' gap={2}>
      {data.map((item, index) => (
        <Flex key={index} gap={2} alignItems='center'>
          <Box w={3} h={3} borderRadius='full' bg={item.color} />
          <Box fontSize='sm'>
            <Flex gap={1} alignItems='center'>
              <Box as='span' fontWeight='bold'>
                {item.name}
              </Box>
              <Box as='span' color={item.color} ml={1} fontWeight='bold'>
                {item.value}
              </Box>
              {item.count && (
                <Box color='gray.500' fontWeight='bold'>
                  ({item.count.toLocaleString()})
                </Box>
              )}
            </Flex>
          </Box>
        </Flex>
      ))}
    </VStack>
  )
}
