import EmptyResult from '@/components/EmptyResult'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import EstimatedValue from '@/components/EstimatedValueDashboard'
import CardLoading from '@/components/loading/Card'
import Modal from '@/components/modal'
import NavigationContainer from '@/components/Navigation'
import PageContainer from '@/components/pageContainer/PageContainer'
import PageHeader from '@/components/PageHeader'
import { RenderWhenFetched } from '@/components/RenderIf'
import TodoCard from '@/components/TodoCard'
import { DrawerBody, DrawerCloseTrigger, DrawerContent, DrawerHeader, DrawerRoot } from '@/components/ui/drawer'
import { toaster } from '@/components/ui/toaster'
import { UnitErrorBox } from '@/components/UnitErrorBox'
import { useAuth } from '@/context/new-auth'
import { approveCompanyDelegation, rejectCompanyDelegation } from '@/endpoints/company'
import { getTodoForAssociate } from '@/endpoints/home'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Center, Flex, Grid, GridItem, Stack } from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React, { useEffect } from 'react'
import { PiWarningCircle } from 'react-icons/pi'
import { useQuery as GraphUseQuery } from 'urql'
import { v4 as uuidv4 } from 'uuid'
import Captable, { Top5ShareholdersFragment } from './Captable'
import EsopStats from './EsopStats'
import LinkHeaderCard from './LinkHeaderCard'

export const CapTableQuery = graphql(
  `
    query CapTableQuery($id: ID!) {
      company(id: $id) {
        register {
          ...Top5ShareholdersFragment
          ...EstimatedValue
        }
      }
    }
  `,
  [Top5ShareholdersFragment, EstimatedValue.registerFragment]
)

export function ManagerView() {
  const { t, lng } = useEbanaLocale()

  const { name, activeWorkspace, hasPermission, switchWorkspace } = useAuth()

  const router = useRouter()
  const [open, setOpen] = React.useState(false)
  const [taskOpen, setTaskOpen] = React.useState(false)

  const [{ data }] = GraphUseQuery({
    query: CapTableQuery,
    variables: {
      id: activeWorkspace.id,
    },
  })

  function onGotIt() {
    switchWorkspace(null)
  }

  const todos = useQuery({
    queryKey: ['home-todo-for-associate'],
    queryFn: getTodoForAssociate,
    refetchOnWindowFocus: false,
    retry: false,
  })

  function todoCallback(item, action) {
    if (item.todoType === 'COMPANY_REGISTRANT_DELEGATION_APPROVAL') {
      if (action === 'approve') {
        approveDelegate(item.id)
      }
      if (action === 'reject') {
        rejectDelegate(item.id)
      }
    }
  }

  function approveDelegate(id) {
    approveCompanyDelegation(id).then(
      (res) => {
        toaster.create({
          title: t('approve-companyDelegation-success-msg'),
          type: 'success',
          closable: true,
        })
        todos.refetch()
      },
      (err) => {
        err.showServerError(err)
      }
    )
  }

  function rejectDelegate(id) {
    rejectCompanyDelegation(id).then(
      (res) => {
        toaster.create({
          title: t('reject-companyDelegation-success-msg'),
          type: 'success',
          closable: true,
        })
        todos.refetch()
      },
      (err) => {
        err.showServerError(err)
      }
    )
  }

  let createAssembly, createResolution, createBoardMeeting

  if (hasPermission('ASSEMBLY_DRAFTING')) {
    createAssembly = (
      <LinkHeaderCard
        id='assembly_shortcut'
        title={t('assembly_shortcut')}
        src='/assets/two-users-green.svg'
        onClick={() =>
          router.push(
            route({ pathname: '/[lng]/company/[companyId]/assemblies', query: { lng, companyId: activeWorkspace.id } })
          )
        }
      />
    )

    createBoardMeeting = (
      <LinkHeaderCard
        id='board_meeting_shortcut'
        title={t('board_meeting_shortcut')}
        src='/assets/board-mgmt.svg'
        onClick={() =>
          router.push(
            route({
              pathname: '/[lng]/company/[companyId]/board/meetings/new',
              query: { lng, companyId: activeWorkspace.id },
            })
          )
        }
      />
    )

    createResolution = (
      <LinkHeaderCard
        id='resolution_shortcut'
        title={t('resolution_shortcut')}
        src='/assets/resolution-icon.svg'
        onClick={() =>
          router.push(
            route({ pathname: '/[lng]/company/[companyId]/resolutions', query: { lng, companyId: activeWorkspace.id } })
          )
        }
      />
    )
  }

  const firstFiveTodos = todos?.data?.slice(0, 4)

  const canApprove =
    activeWorkspace.type === 'company' && activeWorkspace.isTopAuthority && !activeWorkspace.entityApproved
  useEffect(() => {
    if (canApprove) {
      router.push(
        route({ pathname: '/[lng]/company-registration/approval', query: { lng, companyId: activeWorkspace.id } })
      )
    }
  }, [activeWorkspace.id, canApprove, lng, router])
  useEffect(() => {
    if (!open && !canApprove) {
      setOpen(true)
    }
  }, [open, canApprove])

  return (
    <NavigationContainer>
      <PageContainer
        Header={
          <PageHeader
            pageTitle={t('welcome')}
            pageSubTitle={`${t('hi')} ${name.first} ${t('comma')}`}
            flipTitles={true}
          />
        }>
        {activeWorkspace.type === 'company' && !activeWorkspace.isTopAuthority && !activeWorkspace.entityApproved && (
          <Modal
            open={open}
            canClose={false}
            setOpen={setOpen}
            title={t('WAITING_APPROVAL')}
            Body={<Center>{t('waiting_for_company_approval_message')}</Center>}
            Action={
              <Button onClick={onGotIt} colorScheme='primary' w='100%'>
                {t('got_it')}
              </Button>
            }
          />
        )}
        <Flex
          gap='1.25em'
          overflow='scroll'
          flexWrap='wrap'
          css={{
            filter: activeWorkspace.type === 'company' && activeWorkspace.entityApproved ? 'blur(0)' : 'blur(5px)',
            pointerEvents: activeWorkspace.type === 'company' && activeWorkspace.entityApproved ? 'auto' : 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            '&::MsOverflowStyle': 'none',
            scrollbarWidth: 'none',
          }}>
          {createAssembly}
          {createBoardMeeting}
          {createResolution}
        </Flex>
        <Grid
          mt='1.562em'
          templateColumns={{
            base: 'repeat(1, 1fr)',
            md: 'repeat(1, 1fr)',
            lg: 'repeat(3, 1fr)',
          }}
          gap='1.25em'
          gridAutoFlow='dense'
          justifyContent='center'>
          <RenderWhenFetched
            loading={!data || !data?.company?.register}
            render={() => {
              return (
                <>
                  <ErrorBoundary fallback={<UnitErrorBox title={t('cap_table_summary')} />}>
                    <Captable data={data.company.register} />
                  </ErrorBoundary>
                  <ErrorBoundary fallback={<UnitErrorBox title={t('estimated_market_value')} />}>
                    <EstimatedValue
                      registerData={data.company.register}
                      locked={activeWorkspace.type === 'company' && !activeWorkspace.entityApproved}
                    />
                  </ErrorBoundary>
                </>
              )
            }}
          />

          <GridItem colSpan={{ base: 2, md: 1 }} rowSpan={2}>
            <ErrorBoundary fallback={<UnitErrorBox title={t('tasks')} />}>
              <Box background='background.400' py='1.5625em' px='1.875em' borderRadius='14px' minHeight='100%'>
                <div id='associate-todo'>
                  <h4>
                    <Box fontSize='1.2em' fontWeight={700} color='#121423'>
                      {t('to-do')}
                    </Box>
                  </h4>
                  {todos.isLoading ? (
                    <CardLoading numberOfCards={1} numberOfCol={1} />
                  ) : todos.data && todos.data?.length > 0 ? (
                    <Stack gap={0}>
                      {firstFiveTodos?.map((todo, index) => {
                        return (
                          <TodoCard
                            key={uuidv4()}
                            lng={lng}
                            title={lng === 'ar' ? t(todo.titleAr) : t(todo.titleEn)}
                            status={todo.status}
                            subTitle={lng === 'ar' ? t(todo.subTitleAr) : t(todo.subTitleEn)}
                            actions={todo.actions}
                            actionCallback={(item, action) => todoCallback(item, action)}
                            todo={todo}
                            logo={todo.logo}
                          />
                        )
                      })}
                      <Box textAlign='center'>
                        <Button
                          variant='plain'
                          onClick={() => setTaskOpen(true)}
                          fontSize='sm'
                          color='primary.200'
                          _hover={{ textDecoration: 'none' }}>
                          {t('view_more_tasks')}
                        </Button>
                        <Box ms='1rem'>
                          <PiWarningCircle color='#00BFB2' />
                        </Box>
                      </Box>
                    </Stack>
                  ) : (
                    <Box mt='5em'>
                      <EmptyResult title={t('no-todo-found')} description='' />
                    </Box>
                  )}
                </div>
              </Box>
            </ErrorBoundary>
          </GridItem>
          <ErrorBoundary fallback={<UnitErrorBox title={t('esop-status')} />}>
            <EsopStats
              locked={activeWorkspace.type === 'company' && !activeWorkspace.entityApproved}
              companyId={activeWorkspace.id}
            />
          </ErrorBoundary>
        </Grid>

        <DrawerRoot
          open={taskOpen}
          onOpenChange={({ open }) => setTaskOpen(open)}
          placement='end'
          size={{ base: 'xs', md: 'xs', lg: 'xs', sm: 'full' }}>
          <DrawerContent borderTopStartRadius='14px'>
            <DrawerCloseTrigger />
            <DrawerHeader>
              <h3>{t('to-do')}</h3>
            </DrawerHeader>
            <DrawerBody pb='1.875em' pt='2.9375rem'>
              {todos.data?.map((todo, index) => {
                return (
                  <React.Fragment key={uuidv4()}>
                    <TodoCard
                      lng={lng}
                      title={lng === 'ar' ? t(todo.titleAr) : t(todo.titleEn)}
                      status={todo?.status}
                      subTitle={lng === 'ar' ? t(todo?.subTitleAr) : t(todo?.subTitleEn)}
                      actions={todo?.actions}
                      actionCallback={(item, action) => todoCallback(item, action)}
                      todo={todo}
                      logo={todo?.logo}
                    />
                  </React.Fragment>
                )
              })}
            </DrawerBody>
          </DrawerContent>
        </DrawerRoot>
      </PageContainer>
    </NavigationContainer>
  )
}
