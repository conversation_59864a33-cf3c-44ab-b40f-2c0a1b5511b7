import MarketValueChart from '@/features/register/MarketValueChart'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { GridItem, Stack } from '@chakra-ui/react'

EstimatedValue.registerFragment = graphql(
  `
    fragment EstimatedValue on ShareholderRegister {
      estimatedMarketCap {
        formatted
      }
      ticker {
        change {
          complement
          complementFormatted
          formatted
          value
        }
        weekly {
          __typename
        }
        last {
          formatted
          value
        }
      }
      ...MarketValueChart
    }
  `,
  [MarketValueChart.registerFragment]
)

EstimatedValue.equityFragment = graphql(
  `
    fragment EquityEstimatedValue on EquityPositionRegister {
      estimatedMarketCap {
        formatted
      }
      ticker {
        change {
          complement
          complementFormatted
          formatted
          value
        }
        weekly {
          __typename
        }
        last {
          formatted
          value
        }
      }
      ...EquityMarketValueChart
    }
  `,
  [MarketValueChart.equityFragment]
)
export default function EstimatedValue({
  registerData,
  equityData,
}: {
  registerData?: FragmentOf<typeof EstimatedValue.registerFragment>
  equityData?: FragmentOf<typeof EstimatedValue.equityFragment>
  locked?: boolean
  isTitle?: boolean
}) {

  if (registerData == null && equityData == null) return

  const registerResult = readFragment(EstimatedValue.registerFragment, registerData)
  const equityResult = readFragment(EstimatedValue.equityFragment, equityData)

  const { ticker } = registerResult || equityResult

  let renderedContent

  if (ticker == null || ticker.weekly.length === 0) {
    return
  }

  renderedContent = (
    <Stack gap='2.25em'> 
      <MarketValueChart equityData={equityResult} registerData={registerResult} withXAxis={false} withYAxis={false} withCartesianGrid={false} chartHeight={200} />
    </Stack>
  )

  return (
    <GridItem colSpan={2}>
      {renderedContent}
    </GridItem>
  )
}
