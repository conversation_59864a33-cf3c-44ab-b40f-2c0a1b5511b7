import { useTranslation } from '@/app/i18n/client'
import { ApplicationPropsContext } from '@/context/application-props'
import { graphql } from '@/graphql'
import React from 'react'
import Pie<PERSON>hartWithList from '../PieChartWithList'

ShareclassesChart.fragment = graphql(`
  fragment ShareclassesChart on ShareholderRegister {
    capital {
      issuedShares {
        formatted
      }
      shareClasses {
        prefix
        name
        issuedShares {
          value
          formatted
        }
      }
    }
  }
`)

export default function ShareclassesChart({ data }: { data: any }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const pieData = data.capital.shareClasses.map((shareClass: any) => ({
    label: `${shareClass.name} (${shareClass.prefix})`,
    value: shareClass.issuedShares.value,
    displayedValue: shareClass.issuedShares.formatted,
  }))

  return (
    <PieChartWithList
      data={pieData}
      colors={['#6490EB', '#00BFB2', '#FEC412', '#924EF8', '#FF5630', '#003654']}
      width={240}
      height={240}
      showAvatar
      showTotal
      customTitle={t('total-shares')}
      customValue={data.capital.issuedShares.formatted}
    />
  )
}
