import { ErrorBoundary } from '@/components/ErrorBoundary'
import { UnitErrorBox } from '@/components/UnitErrorBox'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Card, Flex, Link, SimpleGrid, Stack, Text } from '@chakra-ui/react'
import { GetOnboardingCards } from './GetOnboardingCards'

OnboardingCards.fragment = graphql(`
  fragment OnboardingProgress on CompanyOnboarding {
    logo
    stamp
    board
    capTable
    committees
    incentives
  }
`)

export default function OnboardingCards({ data }: { data: FragmentOf<typeof OnboardingCards.fragment> }) {
  const { t } = useEbanaLocale()

  const onboarding = readFragment(OnboardingCards.fragment, data)
  const { logo, stamp, board, capTable, committees, incentives } = onboarding

  const cards = GetOnboardingCards({
    onboarding: { logo, stamp, board, capTable, committees, incentives },
  })

  if (Object.values(onboarding).every(Boolean)) return null

  return (
    <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
      <SimpleGrid columns={{ base: 1, md: 2 }} gap={6} w='100%' borderWidth='1px' borderColor='#E2E8F0'>
        {cards
          .filter((card) => !card.view)
          .map((card) => (
            <Card.Root key={card.id} p={4} rounded='1rem' minW={{ base: '100%', md: 'auto' }}>
              <Flex h='full' w='100%' gap={4}>
                <Stack>
                  <Box color='#00263A' textStyle='body3' fontWeight={600} mb={3}>
                    {card.title}
                  </Box>
                  <Text textStyle='body4' lineHeight='1.5714285714' h='5rem' color='#4E5D78'>
                    {card.description}
                  </Text>
                  <Link textDecoration='none' href={card.link}>
                    <Button variant='outline' size='sm' color='#00263A'>
                      {card.actionLabel}
                    </Button>
                  </Link>
                </Stack>
                {card.icon}
              </Flex>
            </Card.Root>
          ))}
      </SimpleGrid>
    </ErrorBoundary>
  )
}
