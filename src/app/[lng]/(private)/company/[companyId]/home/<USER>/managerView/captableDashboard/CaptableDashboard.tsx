import { useTranslation } from '@/app/i18n/client'
import CardContainer from '@/components/CardContainer'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { Box, Button, Flex, Link, Separator, Tabs, Text } from '@chakra-ui/react'
import NextLink from 'next/link'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React, { useState } from 'react'
import ShareClassesChart from './ShareclassesChart'
import ShareholdersChart from './ShareholdersChart'

CaptableDashboard.fragment = graphql(
  `
    fragment CaptableCharts on ShareholderRegister {
      totalShareholders {
        formatted
        value
      }
      top5 {
        ...ShareholdersChart
      }
      capital {
        ...ShareClassesChart
      }
    }
  `,
  [ShareholdersChart.fragment, ShareClassesChart.fragment]
)

export default function CaptableDashboard({ data }: { data: FragmentOf<typeof CaptableDashboard.fragment> }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const [value, setValue] = useState('1')
  const router = useRouter()

  const { id } = React.useContext(CompanyContext)

  const { totalShareholders, top5, capital } = readFragment(CaptableDashboard.fragment, data)

  return (
    <CardContainer px={0} pt='1em' pb='0.5rem' borderRadius='14px' borderStyle='solid'>
      <Flex
        px={{ base: '1em', md: '2em' }}
        mb='1em'
        // direction={{ base: 'column', md: 'row' }}
        justifyContent='space-between'
        // alignItems={{ base: 'flex-start', md: 'center' }}
        gap='0.75rem'>
        <Text my='auto' fontSize={{ base: '1.1em', md: '1.3em' }} fontWeight={700} color='#121423'>
          {t('cap_table_summary')}
        </Text>
        <Link asChild>
          <NextLink href={route({ pathname: '/[lng]/company/[companyId]/captable', query: { lng, companyId: id } })}>
            <Button variant='secondary' px='1.5em' py='0.5em' _hover={{ bg: 'secondary.100' }}>
              {t('captable_tabs.see_more_details')}
            </Button>
          </NextLink>
        </Link>
      </Flex>

      <Separator borderColor='gray.200' />

      <Tabs.Root value={value} onValueChange={({ value }) => setValue(value)} variant='plain' mt='18px'>
        <Flex
          direction={{ base: 'column', md: 'row' }}
          flexWrap='wrap'
          justifyContent='space-between'
          alignItems={{ base: 'flex-start', md: 'center' }}
          px={{ base: '1em', md: '2em' }}
          py='.5em'
          gap='0.75rem'>
          <Text textStyle='body3' fontWeight={600}>
            {value === '1'
              ? totalShareholders?.value > 5
                ? t('captable_tabs.shareholders_text')
                : t('captable_tabs.shareholders')
              : t('captable_tabs.share_classes')}
          </Text>

          <Tabs.List bg='#F2F3F5' py='0.25rem' px='0.3em' borderRadius='full' gap={2} w='fit-content' flexWrap='wrap'>
            {[
              { label: t('captable_tabs.shareholders'), value: '1' },
              { label: t('captable_tabs.share_classes'), value: '2' },
            ].map(({ label, value: tabValue }) => (
              <Tabs.Trigger key={tabValue} value={tabValue} asChild _selected={{ bg: 'white', color: '#00263A' }}>
                <Button
                  px='1.5em'
                  py='0.5em'
                  borderRadius='full'
                  bg={value === tabValue ? 'white' : 'transparent'}
                  color={value === tabValue ? '#00263A' : 'gray.500'}
                  fontWeight='semibold'
                  _hover={{ bg: value === tabValue ? 'white' : 'gray.200' }}>
                  {label}
                </Button>
              </Tabs.Trigger>
            ))}
          </Tabs.List>
        </Flex>

        <Box me={{ base: '1em', md: '2em' }}>
          <Tabs.Content value='1'>
            <ShareholdersChart data={top5} totalShareholders={totalShareholders} />
          </Tabs.Content>
          <Tabs.Content value='2'>
            <ShareClassesChart data={capital} />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </CardContainer>
  )
}
