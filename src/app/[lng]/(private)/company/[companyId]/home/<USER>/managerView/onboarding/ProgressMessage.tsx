import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Flex, Text } from '@chakra-ui/react'
import CustomC<PERSON><PERSON>tti from './CustomConfetti'

export function ProgressMessage({ numberComplete }: { numberComplete: number }) {
  const { t } = useEbanaLocale()

  switch (numberComplete) {
    case 0:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            🚀{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.get_started')}</Text>
        </Flex>
      )
    case 1:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            👍{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.you_are_doing_great')}</Text>
        </Flex>
      )
    case 2:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            💪{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.good_job')}</Text>
        </Flex>
      )
    case 3:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            🚴‍♂️{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.keep_going')}</Text>
        </Flex>
      )
    case 4:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            👉🏻{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.the_finish_near')}</Text>
        </Flex>
      )
    case 5:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            👏🏻{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.just-step')}</Text>
        </Flex>
      )
    case 6:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            🎉{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.you_are_done')}</Text>
          <>
            <CustomConfetti />
          </>
        </Flex>
      )
    default:
      return (
        <Flex>
          <Text fontSize='1.5rem' as='span'>
            {' '}
            🚀{' '}
          </Text>
          <Text textStyle='body3'>{t('onboarding.get_started')}</Text>
        </Flex>
      )
  }
}
