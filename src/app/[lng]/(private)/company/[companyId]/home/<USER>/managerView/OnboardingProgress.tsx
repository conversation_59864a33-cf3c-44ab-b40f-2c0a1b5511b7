import { useTranslation } from '@/app/i18n/client'
import { CheckCircleIcon } from '@/components/EbanaIcons'
import Modal from '@/components/modal'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { Box, Flex, Heading, HStack, Link, Progress, Separator, Stack, Text } from '@chakra-ui/react'
import React, { useRef, useState } from 'react'
import { IoMdClose } from 'react-icons/io'
import { LuChevronRight } from 'react-icons/lu'
import CustomConfetti from './CustomConfetti'
import { getOnboardingCards } from './EmptyStateCards'

OnboardingProgress.fragment = graphql(`
  fragment OnboardingProgress on CompanyOnboarding {
    logo
    stamp
    board
    capTable
    committees
    incentives
  }
`)

function OnboardingModal({
  percentComplete,
  onboarding,
  setOpen,
  open,
}: {
  percentComplete: number
  onboarding: any
  setOpen: (open: boolean) => void
  open: boolean
}) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')
  const { id } = React.useContext(CompanyContext)

  const cards = getOnboardingCards({ t, lng, companyId: id, onboarding: onboarding })

  const headerRef = useRef<HTMLDivElement>(null)

  function progressMessage(numberComplete: number) {
    switch (numberComplete) {
      case 0:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              🚀{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.get_started')}</Text>
          </HStack>
        )
      case 1:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              👍{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.you_are_doing_great')}</Text>
          </HStack>
        )
      case 2:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              💪{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.good_job')}</Text>
          </HStack>
        )
      case 3:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              🚴‍♂️{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.keep_going')}</Text>
          </HStack>
        )
      case 4:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              👉🏻{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.the_finish_near')}</Text>
          </HStack>
        )
      case 5:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              👏🏻{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.just-step')}</Text>
          </HStack>
        )
      case 6:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              🎉{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.you_are_done')}</Text>
            <>
              <CustomConfetti />
            </>
          </HStack>
        )
      default:
        return (
          <HStack>
            <Text fontSize='1.5rem' as='span'>
              {' '}
              🚀{' '}
            </Text>
            <Text textStyle='body3'>{t('onboarding.get_started')}</Text>
          </HStack>
        )
    }
  }

  const body = (
    <Box p='0'>
      <Box overflow='hidden' position='relative' ref={headerRef} bg='#131A2A' color='white' borderTopRadius='lg'>
        <Stack p='4'>
          <Heading as='h4' size='md' color='white' display='flex' alignItems='center' justifyContent='space-between'>
            {t('onboarding.checklist')}
            <IoMdClose cursor='pointer' color='white' onClick={() => setOpen(false)} />
          </Heading>
        </Stack>
        <Separator orientation='horizontal' borderColor='#4E5D78' />
        <Box p='4'>
          <HStack mb='2' justifyContent='space-between'>
            {progressMessage(Object.values(onboarding).filter(Boolean).length)}
            <Text fontWeight='bold' fontSize='sm'>
              {percentComplete}%
            </Text>
          </HStack>

          <Progress.Root value={percentComplete} w='100%' mb='2' border='none'>
            <Progress.Track>
              <Progress.Range bg='primary.500' transition='width 1s ease-in-out' />
            </Progress.Track>
          </Progress.Root>
          <Text fontSize='sm' fontWeight='medium' color='whiteAlpha.800'>
            {t('onboarding.step')} {Object.values(onboarding).filter(Boolean).length}/{Object.keys(onboarding).length}
          </Text>
        </Box>
      </Box>

      <Stack gap={4} p='4' overflowY='auto' height='fit-content' maxH='calc(85vh - 200px)'>
        {cards.map((card: any) => {
          const isCompleted = onboarding[card.id]
          return (
            <Link
              key={card.id}
              href={!isCompleted ? card.link : undefined}
              cursor={isCompleted ? 'default' : 'pointer'}
              _hover={{ textDecoration: 'none' }}
              _focus={{ boxShadow: 'none', outline: 'none' }}>
              <Flex
                px={4}
                py={2}
                borderRadius='1rem'
                borderWidth='1px'
                borderColor='#DADDE4'
                align='center'
                gap={4}
                w='100%'
                bg='white'
                _hover={{ boxShadow: isCompleted ? 'none' : 'sm' }}>
                <Box
                  bg='gray.50'
                  borderRadius='full'
                  p={2}
                  display='flex'
                  alignItems='center'
                  justifyContent='center'
                  boxSize='56px'
                  flexShrink={0}
                  filter={isCompleted ? 'grayscale(100%)' : 'none'}>
                  {card.icon}
                </Box>
                <Box flex='1'>
                  <Heading color={isCompleted ? 'primary.500' : '#00263A'} as='h5' size='md' mb={1}>
                    {card.title}
                  </Heading>
                  <Text fontSize='0.6rem;' color='#4E5D78'>
                    {card.description}
                  </Text>
                </Box>
                <Box pl='2'>
                  {isCompleted ? (
                    <CheckCircleIcon />
                  ) : (
                    <Box transform={lng === 'ar' ? 'scaleX(-1)' : 'none'}>
                      <LuChevronRight fontWeight='400' size={26} color='#00263A' />
                    </Box>
                  )}
                </Box>
              </Flex>
            </Link>
          )
        })}
      </Stack>
      <Flex
        bg='white'
        position='fixed'
        bottom='3vh'
        left={{ base: '50%', md: lng === 'ar' ? '11em' : 'unset' }}
        right={{ base: 'unset', md: lng === 'en' ? '8em' : 'unset' }}
        transform={{ base: 'translateX(-50%)', md: 'none' }}
        px='1.5rem'
        py='0.75rem'
        borderRadius='full'
        align='center'
        gap='1rem'
        fontWeight='bold'
        fontSize='md'
        w='max-content'
        justifyContent='space-between'>
        <Text>{t('onboarding.onboarding')} </Text>
        <Separator orientation='vertical' h='1.5rem' />
        <Text>
          {percentComplete}% {t('onboarding.complete')}
        </Text>
      </Flex>
    </Box>
  )

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      Body={body}
      size='md'
      right={{ base: 'unset', md: lng === 'en' ? '5vh' : 'unset' }}
      left={{ base: 'unset', md: lng === 'ar' ? '5vh' : 'unset' }}
      style={{
        marginInline: 'unset',
        maxWidth: '30rem',
        position: 'fixed',
        top: '3vh',
        padding: '0',
      }}
    />
  )
}

export function OnboardingProgress({ data }: { data: FragmentOf<typeof OnboardingProgress.fragment> }) {
  const [open, setOpen] = useState(false)
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { logo, stamp, board, capTable, committees, incentives } = readFragment(OnboardingProgress.fragment, data)

  const onboarding = [logo, stamp, board, capTable, committees, incentives]
  const totalSteps = onboarding.length
  const completedSteps = onboarding.filter(Boolean).length
  const percentComplete = Math.round((completedSteps / totalSteps) * 100)

  return (
    <>
      <Flex
        bg='#00263A'
        color='white'
        px='1.5rem'
        py='0.75rem'
        borderRadius='full'
        align='center'
        gap='1rem'
        fontWeight='bold'
        fontSize='md'
        w='100%'
        justifyContent='space-between'
        cursor='pointer'
        onClick={() => setOpen(true)}>
        <Text>{t('onboarding.onboarding')} </Text>
        <Separator orientation='vertical' borderColor='white' h='1.5rem' />
        <Text>
          {percentComplete}% {t('onboarding.complete')}
        </Text>
      </Flex>
      <OnboardingModal setOpen={setOpen} open={open} percentComplete={percentComplete} onboarding={{
        logo, stamp, board, capTable, committees, incentives
      }} />
    </>
  )
}
