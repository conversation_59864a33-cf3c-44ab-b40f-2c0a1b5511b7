import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Image, Stack, Tabs } from '@chakra-ui/react'
import React from 'react'

export function KeyServices() {
  const { t } = useEbanaLocale()

  const [width, setWidth] = React.useState<number>()

  const handleResize = React.useCallback((ref) => {
    if (ref) {
      setWidth(ref.offsetWidth)
    }
  }, [])

  return (
    <Stack ref={handleResize} mt='2rem'>
      <Box textStyle='body1' fontWeight={600} color='#121423'>
        {t('ad_key_services')}
      </Box>
      <Tabs.Root display={width <= 1717 ? 'flex' : 'none'} defaultValue='0' mt='1rem' flexDirection='column' gap='2em'>
        <Box width='100%'>
          <Tabs.List>
            <Tabs.Trigger
              p='1.2rem'
              value='0'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              _before={{
                display: 'none',
              }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} width='17.18rem'>
                {t('organizing_meetings_and_decisions')}
              </Box>
            </Tabs.Trigger>
            <Tabs.Trigger
              p='1.2rem'
              value='1'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              _before={{
                display: 'none',
              }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} width='9.9rem'>
                {t('electronic_voting')}
              </Box>
            </Tabs.Trigger>
            <Tabs.Trigger
              p='1.2rem'
              value='2'
              borderRadius='14px'
              border={0}
              bg='#FFFFFF80'
              color='black'
              _before={{
                display: 'none',
              }}
              _selected={{ bg: '#D2D4F8', color: 'black' }}>
              <Box textStyle='body5' fontWeight={600} width='16.12rem'>
                {t('employee_stock_option_program')}
              </Box>
            </Tabs.Trigger>
          </Tabs.List>
        </Box>
        <Tabs.Content value='0' p={0}>
          <DetailsCard
            title={t('organizing_meetings_and_decisions')}
            subtitle={t('organizing_meetings_and_decisions_details')}
            img='/assets/Meeting-room.png'
            img2={undefined}
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
        <Tabs.Content value='1' p={0}>
          <DetailsCard
            title={t('electronic_voting')}
            subtitle={t('electronic_voting_details')}
            img='/assets/assembly-example1.png'
            img2='/assets/assembly-example2.png'
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
        <Tabs.Content value='2' p={0}>
          <DetailsCard
            title={t('employee_stock_option_program')}
            subtitle={t('employee_stock_option_program_details')}
            img='/assets/esop-management.png'
            img2={undefined}
            color='rgba(85,236,225,0.3)'
          />
        </Tabs.Content>
      </Tabs.Root>
      <Flex width='100%' gap='1rem' display={width > 1717 ? 'flex' : 'none'}>
        <DetailsCard
          title={t('organizing_meetings_and_decisions')}
          subtitle={t('organizing_meetings_and_decisions_details')}
          img='/assets/Meeting-room.png'
          img2={undefined}
          color='rgba(85,236,225,0.3)'
        />
        <DetailsCard
          title={t('electronic_voting')}
          subtitle={t('electronic_voting_details')}
          img='/assets/assembly-example1.png'
          img2='/assets/assembly-example2.png'
          color='rgba(85,236,225,0.3)'
        />
        <DetailsCard
          title={t('employee_stock_option_program')}
          subtitle={t('employee_stock_option_program_details')}
          img='/assets/esop-management.png'
          img2={undefined}
          color='rgba(85,236,225,0.3)'
        />
      </Flex>
    </Stack>
  )
}

function DetailsCard({ title, subtitle, img, img2, color }) {
  const { t, lng } = useEbanaLocale()
  const percent = lng === 'ar' ? '60%' : '40%'
  return (
    <Stack
      position='relative'
      gap='1rem'
      p='30px'
      height='370px'
      width={{ base: '100%', md: 'calc(33.3% - 0.667rem)' }}
      minW={{base: '100%', md: '716px' }}
      borderRadius='14px'
      bg={`radial-gradient(circle at ${percent} 60%,
        ${color} 0%, rgba(255,255,255,1) 60%)`}>
      <Box textStyle='body1' fontWeight={600}>
        {t(title)}
      </Box>
      <Box textStyle='body4'>{t(subtitle)}</Box>
      <Box mt='auto' position='relative' mx='auto' maxWidth='269px' maxHeight='175px'>
        <Image src={img} alt='' />
        {img2 && <Image position='absolute' mt='-143px' ms='-4rem' maxHeight='112px' src={img2} alt='' />}
      </Box>
    </Stack>
  )
}