import { useTranslation } from '@/app/i18n/client'
import Pie<PERSON>hartWithList from '@/components/EbPieChart/NewPieChartWithList'
import { ApplicationPropsContext } from '@/context/application-props'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import React from 'react'

ShareClassesChart.fragment = graphql(`
  fragment ShareClassesChart on ShareholderRegisterCapital {
    issuedShares {
      formatted
    }
    shareClasses {
      prefix
      name
      issuedShares {
        value
        formatted
      }
    }
  }
`)

export default function ShareClassesChart({ data }: { data: FragmentOf<typeof ShareClassesChart.fragment> }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { issuedShares, shareClasses } = readFragment(ShareClassesChart.fragment, data)

  type ShareClass = {
    prefix: string
    name: string
    issuedShares: {
      value: number
      formatted: string
    }
  }

  const pieData = shareClasses.map((shareClass: ShareClass) => ({
    label: `${shareClass.name} (${shareClass.prefix})`,
    value: shareClass.issuedShares.value,
    displayedValue: shareClass.issuedShares.formatted,
  }))

  return (
    <PieChartWithList
      data={pieData}
      width={240}
      height={240}
      showAvatar
      showTotal
      customTitle={t('total-shares')}
      customValue={issuedShares.formatted}
    />
  )
}
