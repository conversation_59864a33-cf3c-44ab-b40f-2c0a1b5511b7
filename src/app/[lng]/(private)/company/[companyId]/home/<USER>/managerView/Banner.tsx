import { BellIcon } from '@/components/EbanaIcons'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Flex } from '@chakra-ui/react'
import React from 'react'
import { IoClose } from 'react-icons/io5'

export function Banner() {
  const { t } = useEbanaLocale()

  const [showBanner, setShowBanner] = React.useState(true)

  return (
    showBanner && (
      <Flex justifyContent='space-between' marginTop='-2.25rem' bg='#5858FF' py='1rem' px='3rem' mb='2rem'>
        <Flex alignItems='center' gap={2}>
          <BellIcon />

          <Box fontWeight='medium' color='#fff'>
            {t('complete_company_profile')}
          </Box>

          <Box width='3px' height='3px' borderRadius='full' bg='#fff' />

          <Box fontWeight='lighter' color='#fff' textStyle='body4'>
            {t('complete_company_profile_details')}
          </Box>

          <Box fontWeight='medium' color='#fff' textDecoration='underline' textUnderlineOffset={4} cursor='pointer'>
            {t('get_started')}
          </Box>
        </Flex>

        <Button variant='plain' onClick={() => setShowBanner(false)}>
          <IoClose fill='#fff' stroke='white' />
        </Button>
      </Flex>
    )
  )
}