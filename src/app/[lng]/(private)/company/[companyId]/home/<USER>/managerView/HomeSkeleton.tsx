import { Flex, Skeleton, Stack } from '@chakra-ui/react'

export default function HomeSkeleton() {
  return (
    <Stack w='1096px' minW='1096px' mx={{ base: '1rem', md: 'auto' }} gap='2rem'>
      <Flex w='10096px' justifyContent='space-between'>
        <Skeleton
          h='70px'
          w='210px'
          maxW='210px'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='secondary.200'
        />
        <Skeleton
          h='70px'
          maxW='210px'
          w='210px'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='gray.100'
        />
        <Skeleton
          h='70px'
          maxW='210px'
          w='210px'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='secondary.200'
        />
        <Skeleton
          h='70px'
          maxW='210px'
          w='210px'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='gray.100'
        />
        <Skeleton
          h='70px'
          maxW='210px'
          w='210px'
          borderRadius='14px'
          borderWidth='1px'
          borderColor='white'
          boxShadow='0 8px 32px white, 0 1.5px 4px white'
          bg='secondary.200'
        />
      </Flex>
      <Flex
        justifyContent='space-between'
        flexWrap={{ base: 'wrap', md: 'nowrap' }}
        gap={{ base: '1rem', md: '1.875rem' }}
        mt={{ base: '1rem', md: '0px' }}>
        {/* main content */}
        <Stack w='100%' gap='2rem'>
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
          <Skeleton
            h='24rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
        </Stack>
        {/* side content */}
        <Stack
          mb='1.4375em'
          flex='1 1 0'
          flexWrap='wrap'
          gap={{ base: '1rem', md: '1.875rem' }}
          maxWidth={{ base: '100%', md: '21.875rem' }}
          width={{ base: '100%', md: '21.875rem' }}
          minW={{ base: '100%', md: '21.875rem' }}>
          <Skeleton
            h='30rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
          <Skeleton
            h='5rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 4px 16px white, 0 1.5px 4px white'
            bg='secondary.200'
          />
          <Skeleton
            h='30rem'
            w='100%'
            borderRadius='14px'
            borderWidth='1px'
            borderColor='white'
            boxShadow='0 8px 32px white, 0 1.5px 4px white'
            bg='gray.100'
          />
        </Stack>
      </Flex>
    </Stack>
  )
}
