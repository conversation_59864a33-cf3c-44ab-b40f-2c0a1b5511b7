import { useTranslation } from '@/app/i18n/client'
import {
  CompanyBoardIcon,
  CompanyCaptableIcon,
  CompanyCommitteeIcon,
  CompanyLogoIcon,
  CompanyPlanIcon,
  CompanyStampIcon,
} from '@/components/EbanaIcons'
import { ApplicationPropsContext } from '@/context/application-props'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { Box, Button, Card, Flex, Link, SimpleGrid, Stack, Text } from '@chakra-ui/react'
import { route } from 'nextjs-routes'
import React from 'react'

EmptyStateCards.fragment = graphql(`
  fragment OnboardingProgress on CompanyOnboarding {
    logo
    stamp
    board
    capTable
    committees
    incentives
  }
`)
export function getOnboardingCards({
  t,
  lng,
  companyId,
  onboarding,
}: {
  t: (key: string) => string
  lng: string
  companyId: string
  onboarding: {
    logo: boolean
    stamp: boolean
    board: boolean
    capTable: boolean
    committees: boolean
    incentives: boolean
  }
}) {
  return [
    {
      view: onboarding.capTable,
      id: 'capTable',
      title: t('setup.captable.title'),
      description: t('setup.captable.description'),
      actionLabel: t('setup.captable.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/captable/setup',
        query: { lng, companyId },
      }),
      icon: <CompanyCaptableIcon />,
    },
    {
      view: onboarding.logo,
      id: 'logo',
      title: t('setup.logo.title'),
      description: t('setup.logo.description'),
      actionLabel: t('setup.logo.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng, companyId },
      }),
      icon: <CompanyLogoIcon />,
    },
    {
      view: onboarding.stamp,
      id: 'stamp',
      title: t('setup.stamp.title'),
      description: t('setup.stamp.description'),
      actionLabel: t('setup.stamp.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng, companyId },
      }),
      icon: <CompanyStampIcon />,
    },
    {
      view: onboarding.board,
      id: 'board',
      title: t('setup.board.title'),
      description: t('setup.board.description'),
      actionLabel: t('setup.board.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/board/update',
        query: { lng, companyId },
      }),
      icon: <CompanyBoardIcon />,
    },
    {
      view: onboarding.committees,
      id: 'committees',
      title: t('setup.committee.title'),
      description: t('setup.committee.description'),
      actionLabel: t('setup.committee.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/committees/setup',
        query: { lng, companyId },
      }),
      icon: <CompanyCommitteeIcon />,
    },
    {
      view: onboarding.incentives,
      id: 'incentives',
      title: t('setup.plan.title'),
      description: t('setup.plan.description'),
      actionLabel: t('setup.plan.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/plan-management/plans',
        query: { lng, companyId },
      }),
      icon: <CompanyPlanIcon />,
    },
  ]
}

export default function EmptyStateCards({ data }: { data: FragmentOf<typeof EmptyStateCards.fragment> }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { id } = React.useContext(CompanyContext)

  const { logo, stamp, board, capTable, committees, incentives } = readFragment(EmptyStateCards.fragment, data)
  const cards = getOnboardingCards({
    t,
    lng,
    companyId: id,
    onboarding: { logo, stamp, board, capTable, committees, incentives },
  })
  return (
    <SimpleGrid columns={{ base: 1, md: 2 }} gap={6} w='100%' borderWidth='1px' borderColor='#E2E8F0'>
      {cards.map((card) => {
        if (card.view) return null
        return (
          <Card.Root key={card.id} p={4} rounded='1rem' minW={{ base: '100%', md: 'auto' }}>
            <Flex h='full' w='100%' gap={4}>
              <Stack>
                <Box color='#00263A' textStyle='body3' fontWeight={600} mb={3}>
                  {card.title}
                </Box>
                <Text textStyle='body4' lineHeight='1.5714285714' h='5rem' color='#4E5D78'>
                  {card.description}
                </Text>
                <Link textDecoration='none' href={card.link}>
                  <Button variant='outline' size='sm' color='#00263A'>
                    {card.actionLabel}
                  </Button>
                </Link>
              </Stack>
              {card.icon}
            </Flex>
          </Card.Root>
        )
      })}
    </SimpleGrid>
  )
}
