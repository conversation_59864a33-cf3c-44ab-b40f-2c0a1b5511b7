'use client'

import NavigationContainer from '@/components/Navigation'
import PageContainer from '@/components/pageContainer/PageContainer'
import PageHeader from '@/components/PageHeader'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React from 'react'
import { CompanyContext } from '@/context/company'

export default function EditCompanyProfileLayout(props) {
  const { children } = props
  const { t, lng } = useEbanaLocale()

  const router = useRouter()

  const company = React.useContext(CompanyContext)

  return (
    <NavigationContainer>
      <PageContainer
        Header={
          // TODO: replace pb in a propper way
          <Box pb='2.7em'>
            <PageHeader
              t={t}
              pageTitle={t('manage_company_profile')}
              action={
                <Button
                  variant='secondary'
                  fontWeight={700}
                  borderWidth='2px'
                  gap='0.7em'
                  px='2.6em'
                  py='1.7em'
                  onClick={() => {
                    router.push(
                      route({
                        pathname: '/[lng]/company/[companyId]/profile',
                        query: { lng, companyId: company.id },
                      })
                    )
                  }}>
                  {t('cancel')}
                </Button>
              }
            />
          </Box>
        }>
        {children}
      </PageContainer>
    </NavigationContainer>
  )
}
