import { EbanaForm } from '@/components/form/EbanaForm'
import DebounceInput from '@/components/input/DebounceInput'
import { PaginationContext } from '@/components/pagination/PaginationContext'
import { usePageWithParam } from '@/components/pagination/hooks/UsePageWithParam'
import { EbanaPagination } from '@/components/pagination/index'
import { useAuth } from '@/context/new-auth'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { FormFragment } from '@/graphql/fragments'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Field, Flex, Heading, Image, InputGroup, Stack } from '@chakra-ui/react'
import React from 'react'
import { useForm } from 'react-hook-form'
import { useMutation } from 'urql'
import FitUnfit from './fit-unfit'
import ShareholderTable from './shareholder-table'

ShareholderList.fragment = graphql(
  `
    fragment ShareholderList on ShareholderRegister {
      fitUnfit {
        enabled
      }
      updateShareholdersFitStatusForm {
        ...FormFragment
      }
      shareholdersPaginated(search: $search, pageSize: $pageSize, page: $page) {
        totalElements
        totalPages
        size
        number
        ...ShareholderTableFragment
      }
    }
  `,
  [ShareholderTable.fragment, FormFragment]
)
export default function ShareholderList({
  data,
  searchState,
}: {
  data: FragmentOf<typeof ShareholderList.fragment>
  searchState: [string, React.Dispatch<React.SetStateAction<string>>]
}) {
  const { t } = useEbanaLocale()

  const { request, setRequest } = usePageWithParam()

  const result = readFragment(ShareholderList.fragment, data)

  const [search, setSearch] = searchState

  const auth = useAuth()
  const tradeName = auth.activeWorkspace.type === 'company' && auth.activeWorkspace.tradeName

  const [selectedShareholders, setSelectedShareholders] = React.useState<{ type: any; value: string }[]>([])

  const methods = useForm({ defaultValues: result.updateShareholdersFitStatusForm.defaultValues })

  const [, mutation] = useMutation(
    graphql(`
      mutation UpdateShareholdersFitStatus($id: ID!, $input: UpdateShareholdersFitStatusInput!) {
        updateShareholdersFitStatus(id: $id, input: $input) {
          id
        }
      }
    `)
  )

  function selectShareholder(shareholder: { type: any; value: string }) {
    if (selectedShareholders.some(({ value }) => value === shareholder.value)) {
      setSelectedShareholders(selectedShareholders.filter(({ value }) => value !== shareholder.value))
    } else {
      setSelectedShareholders([...selectedShareholders, shareholder])
    }
  }

  //   function selectAll() {
  //     if (selectedShareholders.length === shareholders.length) {
  //       setSelectedShareholders([])
  //     } else {
  //       setSelectedShareholders([...shareholders])
  //     }
  //   }

  return (
    <PaginationContext request={request} setRequest={setRequest} page={result.shareholdersPaginated}>
      <Stack>
        <Flex
          flexWrap={{ base: 'wrap', md: 'nowrap' }}
          gap={{ base: '0.5rem', md: '0' }}
          justifyContent='space-between'
          mb='1em'>
          <Heading as='h3' fontSize='1.5em'>
            {t('owners') + ' ' + tradeName}
          </Heading>
          <Flex>
            <Search search={search} setSearch={setSearch} />
          </Flex>
        </Flex>
        {result.fitUnfit?.enabled && (
          <EbanaForm form={result.updateShareholdersFitStatusForm} methods={methods} mutation={mutation}>
            {({ submit }) => (
              <FitUnfit
                onSubmit={(fit) => {
                  submit({ shareholders: selectedShareholders, fit })
                  setSelectedShareholders([])
                }}
                form={result.updateShareholdersFitStatusForm}
                shareholdersCount={selectedShareholders.length}
              />
            )}
          </EbanaForm>
        )}
        <Box
          maxW='100%'
          overflowX='scroll'
          css={{
            md: {
              '&::-webkit-scrollbar': {
                display: 'none',
              },
              '&::MsOverflowStyle': 'none',
              scrollbarWidth: 'none',
            },
          }}>
          <ShareholderTable
            registerFitUnfitEnabled={result.fitUnfit.enabled}
            data={result.shareholdersPaginated}
            selectedShareholders={selectedShareholders}
            selectShareholder={selectShareholder}
          />
        </Box>
        <Box p='1em' pt={0}>
          <EbanaPagination />
        </Box>
      </Stack>
    </PaginationContext>
  )
}

function Search({ search, setSearch }) {
  const { t } = useEbanaLocale()

  return (
    <Field.Root>
      <InputGroup
        endElement={<Image src='/assets/search.svg' width='1.125em' height='1.125em' alt='search' />}
        width='100%'
        p={0}
        borderRadius='10px'>
        <DebounceInput
          value={search}
          onChange={(value) => {
            setSearch(value)
          }}
          borderWidth='1px'
          borderStyle='solid'
          borderColor='stroke.100'
          placeholder={t('search')}
          borderRadius='10px'
          background='white'
          height='3em'
          width='20em'
        />
      </InputGroup>
    </Field.Root>
  )
}
