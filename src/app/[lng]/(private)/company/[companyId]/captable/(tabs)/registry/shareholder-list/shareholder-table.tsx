import { DocumentTextIcon, SemiCrossIcon, UpdateIcon } from '@/components/EbanaIcons'
import { NewStatus } from '@/components/NewStatus'
import { Checkbox } from '@/components/ui/checkbox'

import { Tooltip } from '@/components/ui/tooltip'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, IconButton, Image, Stack, Table } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React from 'react'

ShareholderTable.fragment = graphql(`
  fragment ShareholderTableFragment on ShareholderRegisterShareholderPaginated {
    totalPages
    totalElements
    size
    number
    nodes {
      fitUnfit {
        fit
      }
      certificate {
        url
      }
      updateForm {
        available
      }
      restrictions {
        shareCount {
          formatted
          value
        }
        note
        shareClass {
          prefix
          name
          parValue {
            formatted
          }
          issuedShares {
            formatted
          }
          issuedAmount {
            n {
              formatted
            }
          }
          treasuryShares {
            formatted
          }
        }
        kind {
          value
          label
        }
      }
      name
      id {
        value
        type
        typeName
        encoded
      }
      inputIdType
      totalOwnership {
        formatted(maximumFractionDigits: 1)
      }
      totalShares {
        value
        formatted
      }
      cost {
        formatted
      }
      commonShares {
        value
        formatted
      }
      preferredShares {
        value
        formatted
      }
      redeemableShares {
        value
        formatted
      }
      ownership {
        cost {
          formatted
        }
        shareCount {
          formatted
        }
        shareClass {
          prefix
          name
          issuedShares {
            value
          }
          treasuryShares {
            value
          }
        }
      }
    }
  }
`)

export default function ShareholderTable({
  data,
  selectedShareholders,
  selectShareholder,
  registerFitUnfitEnabled = false,
}: {
  data: FragmentOf<typeof ShareholderTable.fragment>
  selectedShareholders: { type: any; value: string }[]
  selectShareholder: (shareholder: { type: any; value: string }) => void
  registerFitUnfitEnabled?: boolean
}) {
  const { t, lng } = useEbanaLocale()
  const { id: companyId } = React.useContext(CompanyContext)
  const router = useRouter()

  const shareholders = readFragment(ShareholderTable.fragment, data)

  const dataRows = shareholders?.nodes?.map(
    ({ id, inputIdType, name, ownership, totalOwnership, certificate, restrictions, updateForm, fitUnfit }) => {
      return (
        <Table.Row key={id.value}>
          {registerFitUnfitEnabled && (
            <Table.Cell textAlign='center'>
              <Checkbox
                checked={selectedShareholders.some(({ value }) => value === id.value)}
                onCheckedChange={() => selectShareholder({ value: id.value, type: inputIdType })}
              />
            </Table.Cell>
          )}
          <Table.Cell>
            <Flex gap='1em'>
              <Stack gap={0}>
                <Box fontSize='0.75rem'>{name}</Box>
                <Flex gap='0.2em'>
                  <Box color='primary.100'>{id.value}</Box>
                  <Box color='primary.300'>- {id.typeName}</Box>
                </Flex>
              </Stack>
              {restrictions.length ? (
                <Tooltip contentProps={{ bg: '#101828' }} content={t('this_shareholder_have_restrictions')}>
                  <Image src='/assets/warning.svg' height='1.2em' pe='1em' ms='0.3em' alt='!' />
                </Tooltip>
              ) : (
                <></>
              )}
            </Flex>
          </Table.Cell>
          {registerFitUnfitEnabled && (
            <Table.Cell p='0'>
              {!fitUnfit?.fit && (
                <NewStatus label={t('unfit_for_trading')} icon={<SemiCrossIcon fill='primary.400' />} bg='#FFEFEB' />
              )}
            </Table.Cell>
          )}
          <Table.Cell>
            <Stack gap='0.5em'>
              {ownership.map((c, index) => {
                return (
                  <Flex
                    key={index}
                    alignContent='center'
                    borderRadius='50px'
                    bg='background.200'
                    width='fit-content'
                    pe='0.5em'
                    gap='0.4em'>
                    <Box
                      color='white'
                      borderRadius='50px'
                      bg={colorPicker(index)}
                      width='fit-content'
                      px='0.5em'
                      py='0.2em'>
                      {c?.shareCount?.formatted}
                    </Box>
                    <Box my='auto'>{c?.shareClass?.name}</Box>
                  </Flex>
                )
              })}
            </Stack>
          </Table.Cell>

          <Table.Cell>{totalOwnership.formatted}</Table.Cell>
          <Table.Cell>
            <Flex w='fit-content' gap='1em'>
              <Tooltip contentProps={{ bg: '#101828' }} content={t('update_shareholder')}>
                <IconButton
                  disabled={!updateForm.available}
                  aria-label='update'
                  variant='plain'
                  onClick={() => {
                    router.push(
                      route({
                        pathname: '/[lng]/company/[companyId]/captable/registry/shareholder/[shareholderId]/update',
                        query: { lng, companyId, shareholderId: id.encoded },
                      })
                    )
                  }}
                  border={0}
                  width='fit-content'
                  p={0}
                  gap='0.5em'>
                  <UpdateIcon />
                </IconButton>
              </Tooltip>

              {certificate?.url && (
                <a target='_blank' href={certificate?.url}>
                  <Tooltip contentProps={{ bg: '#101828' }} content={t('ownership_certificate')}>
                    <IconButton variant='plain' aria-label='docs' border={0} width='fit-content' p={0} gap='0.5em'>
                      <DocumentTextIcon />
                    </IconButton>
                  </Tooltip>
                </a>
              )}
            </Flex>
          </Table.Cell>
        </Table.Row>
      )
    }
  )

  return (
    <Box
      maxW='100%'
      overflowX='scroll'
      css={{
        md: {
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '&::MsOverflowStyle': 'none',
          scrollbarWidth: 'none',
        },
      }}>
      <Table.ScrollArea minW={{ base: '1440px', md: 'unset' }}>
        <Table.Root
          style={{
            borderSpacing: '0 5px',
          }}>
          <Table.Header>
            <Table.Row>
              {registerFitUnfitEnabled && (
                <Table.ColumnHeader h='3.625rem' w='3.75rem'>
                  {/* <Checkbox
                    checked={selectedShareholders.some((i) => i === id.value)}
                    onCheckedChange={() => selectShareholder(id.value)}
                  /> */}
                </Table.ColumnHeader>
              )}
              <Table.ColumnHeader w='19.75rem' h='3.6875rem'>
                {t('name')}
              </Table.ColumnHeader>
              {registerFitUnfitEnabled && <Table.ColumnHeader w='9.3125rem'></Table.ColumnHeader>}
              <Table.ColumnHeader>{t('ownerships')}</Table.ColumnHeader>
              <Table.ColumnHeader>{t('total_shares')}</Table.ColumnHeader>
              <Table.ColumnHeader w='10em' maxW='10em'>
                {t('')}
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>
          <Table.Body>{dataRows}</Table.Body>
        </Table.Root>
      </Table.ScrollArea>
    </Box>
  )
}

function colorPicker(index) {
  switch ((index + 1) % 4) {
    case 1:
      return 'primary.300'
    case 2:
      return 'primary.100'
    case 3:
      return 'primary.400'
    default:
      return 'primary.600'
  }
}
