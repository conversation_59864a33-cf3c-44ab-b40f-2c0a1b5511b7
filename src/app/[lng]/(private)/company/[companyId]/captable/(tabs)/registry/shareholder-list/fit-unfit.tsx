import { ActiveIcon, SemiCrossIcon } from '@/components/EbanaIcons'
import { DialogBody, DialogContent, DialogHeader, DialogRoot } from '@/components/ui/dialog'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { <PERSON>, <PERSON><PERSON>, DialogFooter, Flex } from '@chakra-ui/react'
import React from 'react'

export default function FitUnfit({
  shareholdersCount,
  onSubmit,
  form,
}: {
  shareholdersCount: number
  onSubmit: (fit: boolean) => void
  form: Form
}) {
  const { t } = useEbanaLocale()

  const [open, setOpen] = React.useState(false)
  const [willFit, setWillFit] = React.useState<boolean>(null)

  return (
    <>
      <Flex
        alignItems='center'
        justifyContent='space-between'
        gap='2em'
        bg='white'
        py='1.125em'
        px='1.5em'
        borderRadius='10px'>
        <Box color='typography.100' fontWeight={600} textStyle='body4'>
          {shareholdersCount === 1
            ? t('shareholder_selected')
            : shareholdersCount === 2
              ? t('two_shareholders_selected')
              : t('n_shareholders_selected', { n: shareholdersCount })}
        </Box>
        <Flex alignItems='center' gap='1.3125em'>
          <Box color='typography.100' textStyle='body4' fontWeight={600}>
            {t('bulk_actions')}:
          </Box>
          <Flex gap='0.875rem'>
            <Button
              disabled={shareholdersCount === 0}
              variant='secondary'
              py='0.65625rem'
              gap='0.375rem'
              onClick={() => {
                setWillFit(true)
                setOpen(true)
              }}>
              <ActiveIcon stroke='none' fill='primary.200' w='14px' h='14px' />
              {t('fit')}
            </Button>
            <Button
              disabled={shareholdersCount === 0}
              variant='secondary'
              py='0.65625rem'
              color='primary.400'
              borderColor='primary.400'
              gap='0.375rem'
              onClick={() => {
                setWillFit(false)
                setOpen(true)
              }}>
              <SemiCrossIcon fill='primary.400' w='14px' h='14px' />
              {t('unfit')}
            </Button>
          </Flex>
        </Flex>
      </Flex>
      <DialogRoot open={open} onOpenChange={({ open }) => setOpen(open)}>
        <DialogContent>
          <DialogHeader>{form.confirmation.title}</DialogHeader>
          <DialogBody>{form.confirmation.body}</DialogBody>
          <DialogFooter>
            <Button
              onClick={() => {
                onSubmit(willFit)
                setOpen(false)
                setWillFit(null)
              }}
              py='0.65625rem'
              gap='0.375rem'>
              {form.submission.action}
            </Button>
            <Button onClick={() => setOpen(false)} variant='secondary' py='0.65625rem'>
              {t('cancel')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>
    </>
  )
}
