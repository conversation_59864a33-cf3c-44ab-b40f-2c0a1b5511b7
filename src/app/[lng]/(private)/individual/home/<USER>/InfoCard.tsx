import { <PERSON>alog<PERSON>ody, DialogCloseTrigger, DialogContent, DialogRoot } from '@/components/ui/dialog'
import { ApplicationPropsContext } from '@/context/application-props'
import { Box, Flex, Image, Stack, Text } from '@chakra-ui/react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { ModalContentItem } from './index'

interface InfoCardProps {
  title: string
  subtitle: string
  bg: string
  img2?: string
  img: string
  modalDescription?: string
  modalContent?: ModalContentItem[]
}
export default function InfoCard({ title, subtitle, bg, img2, img, modalDescription, modalContent }: InfoCardProps) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation('Pages')
  const [open, setOpen] = React.useState<boolean>(false)

  return (
    <>
      <Flex
        flexWrap='wrap'
        gapY='1rem'
        cursor='pointer'
        onClick={() => setO<PERSON>(!open)}
        p='20px'
        bg={bg}
        borderRadius='14px'
        justifyContent='space-between'
        width={{ base: '100%', md: 'calc(33% - 11px)' }}
        minW='200px'
        mx={{ base: 'auto', md: 0 }}>
        <Stack maxW='280px'>
          <Box fontWeight={600} color='#121423'>
            {title}
          </Box>
          <Box mt='0.5rem' textStyle='body4'>
            {subtitle}
          </Box>
          <Box mt='auto' pt='1rem' color='primary.200' fontWeight={700} textStyle='body5'>
            {t('learn_more')}
          </Box>
        </Stack>
        <Image
          ms='auto'
          mt='auto'
          position='relative'
          // bg='#ffffff80'
          borderRadius={10}
          height='142px'
          src={img}
          alt=''
        />
      </Flex>
      <DialogRoot
        open={open}
        onOpenChange={({ open }) => {
          setOpen(open)
        }}
        placement='center'
        size='lg'>
        <DialogContent width='700px' p={0} overflow='auto'>
          <DialogCloseTrigger color='#8A94A6' top='1.5em' insetEnd='1.5em' />
          <Box p='20px' width='100%' maxHeight='64px' height='64px' bg='#192133'>
            <Image height='24px' src={`/assets/logo/${lng}/dark-logo.svg`} alt='' />
          </Box>
          <DialogBody padding='0'>
            <Stack gap='' px='30px' py='20px'>
              <Box color='#00263A' textStyle='h2' fontWeight={700}>
                {t(title)}
              </Box>
              <Box
                ms={{ base: 'auto', md: 0 }}
                pt='8px'
                position='relative'
                justifyContent='center'
                mt='1.25em'
                width='100%'
                bg='#E0EEF5'
                height='195px'
                borderRadius={10}>
                <Image mx='auto' height='100%' src={img} alt='' />
                {img2 && <Image position='absolute' mt='-8.5rem' ms='8.5rem' width='85px' src={img2} alt='' />}
              </Box>

              <Text mt='1rem' textStyle='body4' color='primary.300'>
                {modalDescription}
              </Text>
              <Box
                ms={{ base: 'auto', md: 0 }}
                position='relative'
                justifyContent='center'
                mt='1.25em'
                width='100%'
                bg='white'
                boxShadow='1px 1px 28px 0px #BFBFBF40'
                p='1rem'
                borderRadius={10}>
                {modalContent?.map((content, index) => (
                  <Box key={index}>
                    <Text textStyle='h4' color='#00263A' fontWeight={700}>
                      {content.title}
                    </Text>

                    <Text textStyle='body4' color='#4E5D78' my='0.5rem'>
                      {content.description}
                    </Text>
                  </Box>
                ))}
              </Box>
            </Stack>
          </DialogBody>
        </DialogContent>
      </DialogRoot>
    </>
  )
}
