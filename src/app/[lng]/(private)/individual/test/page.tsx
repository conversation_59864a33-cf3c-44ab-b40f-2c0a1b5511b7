'use client'

import {
  FileUploadDropZone,
  FileUploadItem,
  FileUploadItemContent,
  FileUploadRootProvider,
} from '@/components/ui/file-upload'
import { Box, For, useFileUpload } from '@chakra-ui/react'

export default function Page() {
  const fileUpload = useFileUpload({
    maxFiles: 10,
    // disabled: true,
  })
  const { acceptedFiles: files } = fileUpload

  return (
    <Box bg='white' p='20em'>
      <FileUploadRootProvider value={fileUpload}>
        <FileUploadDropZone />
        <For each={files}>
          {(file) => {
            return (
              <FileUploadItem key={file.name} file={file}>
                <FileUploadItemContent />
              </FileUploadItem>
            )
          }}
        </For>
      </FileUploadRootProvider>
    </Box>
  )
}
